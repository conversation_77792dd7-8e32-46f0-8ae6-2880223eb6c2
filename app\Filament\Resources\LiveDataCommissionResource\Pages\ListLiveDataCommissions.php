<?php

namespace App\Filament\Resources\LiveDataCommissionResource\Pages;

use App\Filament\Resources\LiveDataCommissionResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListLiveDataCommissions extends ListRecords
{
    protected static string $resource = LiveDataCommissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // 不允许直接添加直播佣金记录
            // Actions\CreateAction::make(),
        ];
    }



    public function getTabs(): array
    {
        return [
            'all' => Tab::make('全部条目'),
            'pending' => Tab::make('审核中条目')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('approval_status', 'pending')),
            'rejected' => Tab::make('未通过审核')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('approval_status', 'rejected')),
            'approved' => Tab::make('已通过审核')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('approval_status', 'approved')),
        ];
    }
}
