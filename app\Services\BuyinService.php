<?php

namespace App\Services;

use App\Models\AnchorAccount;
use App\Models\LiveData;
use Illuminate\Support\Str;


class BuyinService
{

    //刷新账号
    public static function refreshAccount($account_uid)
    {
        $anchor_account = AnchorAccount::where('account_uid', $account_uid)->first();
        if (!$anchor_account) {
            throw new \Exception('账号不存在');
        }

        $result = self::get_account_score($account_uid, $credit_score, $seller_rating_score, $good_price_rate);

        $anchor_account->credit_score = $credit_score;
        $anchor_account->reputation_score = $seller_rating_score;
        $anchor_account->price_score = $good_price_rate;
        $anchor_account->save();

        return $result;
    }

    //刷新账号
    public static function refreshLivedata($account_uid)
    {
        //获取当前uuid的账户
        $anchor_account = AnchorAccount::where('account_uid', $account_uid)->first();
        //如果账号不存在,则报错
        if (!$anchor_account) {
            throw new \Exception('账号不存在');
        }

        //获取当前uuid的主播
        $anchor = $anchor_account->anchor;
        if (is_null($anchor)) {
            throw new \Exception('请先将此账号归属于某个主播!');
        }

        $livedata_list = self::get_livedata($account_uid);
        if ($livedata_list == false) {
            throw new \Exception('直播场次数据获取异常!');
        }
        foreach ($livedata_list as $livedata_key => $livedata_item) {
            // dump($livedata_item);
            //查询当前直播场次是否存在, 如果存在则更新, 不存在则新增
            $livedata = LiveData::where('anchor_account_id', $anchor_account->id)
                ->where('anchor_id', $anchor->id)
                ->where('room_id', $livedata_item['room_id'])
                // 如果这里跟上时间戳,会重复, 因为对方服务不稳定这个值会变动.
                // ->where('start_time', $livedata_item['start_time'])
                // ->where('end_time', $livedata_item['end_time'])
                ->first();

            if (!$livedata) {
                $livedata = new LiveData();
                $livedata->anchor_id = $anchor->id;
                $livedata->anchor_account_id = $anchor_account->id;
            }

            $livedata->room_id = $livedata_item['room_id'];
            $livedata->base_info = $livedata_item['base_info'];
            $livedata->start_time = $livedata_item['start_time'];
            $livedata->end_time = $livedata_item['end_time'];

            //start_time和end_time都是Y-m-d H:i:s格式
            //持续秒
            $duration_second= strtotime($livedata_item['end_time']) - strtotime($livedata_item['start_time']);
            $livedata->duration_second = $duration_second;

            $livedata->viewers = $livedata_item['viewers'];
            $livedata->exposure = $livedata_item['exposure'];
            $livedata->clicks = $livedata_item['clicks'];

            $livedata->sales = $livedata_item['sales'];
            $livedata->content_quality = $livedata_item['content_quality'];
            $livedata->save();
        }
        return count($livedata_list);
    }


    //获取信用分
    // $credit_score=0;//用户体验分
    // $seller_rating_score=0;//口碑分
    // $good_price_rate=0;//价格分
    public static function get_account_score($account_uid, &$credit_score = 0, &$seller_rating_score = 0, &$good_price_rate = 0)
    {
        $credit_score = 0;
        $seller_rating_score = 0;
        $good_price_rate = 0;

        $url = "https://buyin.jinritemai.com/api/anchor/workbench/info";
        $web_json_array = CookiesService::getWebJsonCodeByUuid($account_uid, $url);
        if (!$web_json_array) {
            return false;
        }

        //用户体验分
        if (isset($web_json_array['data']['credit_score'])) {
            $credit_score = $web_json_array['data']['credit_score'];
            $credit_score = $credit_score / 100;
        }

        //获取当前年月,本月的口碑分
        $now_y_m = date('Ym');
        $has_data = false;
        if (isset($web_json_array['data']['seller_rating'][$now_y_m]['has_data'])) {
            $has_data = $web_json_array['data']['seller_rating'][$now_y_m]['has_data'];
        }
        if ($has_data) {
            $seller_rating_score = $web_json_array['data']['seller_rating'][$now_y_m]['score'];
        }

        //价格分
        if (isset($web_json_array['data']['good_price_rate'])) {
            $good_price_rate = $web_json_array['data']['good_price_rate'];
        }
        return true;
    }


    // 获取直播场次, 数据如下: 直播场次,直播间观看人次,商品曝光次数,商品点击次数,直播间成交金额,内容质量诊断
    public static function get_livedata($account_uid): array|bool
    {
        $url = "https://buyin.jinritemai.com/api/anchor/livepc/data?page=0&pageSize=10&aggregated_data=1";
        $web_json_array = CookiesService::getWebJsonCodeByUuid($account_uid, $url);
        if (!$web_json_array) {
            return false;
        }
        if (!isset($web_json_array['data']['rooms'])) {
            return false;
        }
        $rooms = $web_json_array['data']['rooms'];
        // 初始化存储所有直播场次的数组
        $livedata = [];
        foreach ($rooms as $room) {
            $roomId = $room['room_id'];
            // 直播描述 title
            $baseInfo = trim($room['title']);
            // 开始时间 create_time
            $startTime = date('Y-m-d H:i:s', $room['create_time']);
            // 结束时间 finish_time
            $endTime = date('Y-m-d H:i:s', $room['finish_time']);
            // 观看次数 watch_pv_entrance_show
            $viewers = $room['watch_pv_entrance_show'];
            // 商品曝光次数 prd_show_cnt
            $exposure = $room['prd_show_cnt'];
            // 商品点击次数 click_pv_product
            $clicks = $room['click_pv_product'];
            // 成交金额 pay_gmv /100 保留2位小数
            $sales = round($room['pay_gmv'] / 100, 2);
            // 内容质量 live_high_quality_stats.quality_result
            $contentQuality = $room['live_high_quality_stats']['quality_result'];
            // 保存到数组
            $livedata[] = [
                'room_id'         => $roomId,
                'base_info'       => $baseInfo,
                'start_time'      => $startTime,
                'end_time'        => $endTime,
                'viewers'         => $viewers,
                'exposure'        => $exposure,
                'clicks'          => $clicks,
                'sales'           => $sales,
                'content_quality' => $contentQuality
            ];
        }
        // 返回JSON格式的数据，或根据需求自定义
        return $livedata;
    }


    //获取某一个场直播的高级统计数据
    public static function get_livedata_detail($account_uid, $room_id)
    {
        // 查找该直播场次数据
        $livedata = LiveData::where('room_id', $room_id)
            ->first();
        if (!$livedata) {
            throw new \Exception('直播场次数据不存在');
        }

        $url = "https://compass.jinritemai.com/compass_api/author/live/basic_live_screen/base_info?room_id={$room_id}";
        $web_json_array = CookiesService::getWebJsonCodeByLUOPANDT($account_uid, $url);

        if (!$web_json_array || !isset($web_json_array['data'])) {
            return false;
        }

        $livedata_data = $web_json_array['data'];
        // dump($livedata);
        // dd($livedata_data);

        $livedata->avg_watch_duration = $livedata_data['avg_watch_duration']['value'];
        $livedata->fans_club_ucnt = $livedata_data['fans_club_ucnt']['value'];
        $livedata->gmv = $livedata_data['gmv'];
        $livedata->gpm = $livedata_data['gpm']['value'];
        $livedata->incr_fans_cnt = $livedata_data['incr_fans_cnt']['value'];
        $livedata->online_user_cnt = $livedata_data['online_user_cnt']['value'];
        $livedata->online_user_ucnt = $livedata_data['online_user_ucnt']['value'];
        $livedata->pay_cnt = $livedata_data['pay_cnt']['value'];
        $livedata->pay_fans_ratio = $livedata_data['pay_fans_ratio']['value'];
        $livedata->pay_ucnt = $livedata_data['pay_ucnt']['value'];
        $livedata->product_click_to_pay_rate = $livedata_data['product_click_to_pay_rate']['value'];

        $livedata->save();

        return $livedata_data;
    }

}
