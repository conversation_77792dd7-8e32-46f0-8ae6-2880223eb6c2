<?php

namespace App\Filament\Resources;

use App\Filament\Actions\LiveDataAction;
use App\Filament\Resources\LiveDataResource\Pages;
use App\Filament\Resources\LiveDataResource\RelationManagers;
use App\Models\LiveData;
use <PERSON><PERSON>hanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Filament\Forms\Get;
use Filament\Forms\Set;

class LiveDataResource extends Resource implements HasShieldPermissions
{

    protected static ?int $navigationSort = 40;

    protected static ?string $model = LiveData::class;


    public static ?string $label = '直播场次';

    protected static ?string $navigationLabel = '直播场次列表';

    protected static ?string $navigationGroup = 'MCN管理';

    protected static ?string $navigationIcon = 'heroicon-o-presentation-chart-line';

    public static function getNavigationGroup(): ?string
    {
        // //判断当前用户是否拥有 运营 角色
        // if (auth()->user()->hasRole('运营')) {
        //     return "直播场次";
        // } else {
        //     return static::$navigationGroup;
        // }
        return static::$navigationGroup;
    }

    public static function getEloquentQuery(): Builder
    {
        //如果是超级管理员,则返回所有
        if (Auth::user()->isSuperAdmin()) {
            return parent::getEloquentQuery();
        }else if (static::can('view_any_user_data')) {
            return parent::getEloquentQuery();
        }else{
            //获取当前用户的数据
            $liveDatas_ids = Auth::user()->liveDatas()->pluck('dymcn_livedata.id')->toArray();
            return parent::getEloquentQuery()->whereIn('id', $liveDatas_ids);
        }
    }


    public static function getPermissionPrefixes(): array
    {
        return array_merge(\BezhanSalleh\FilamentShield\Support\Utils::getGeneralResourcePermissionPrefixes(), [
            //显示任意用户数据功能
            'view_any_user_data',
            //是否可查看直播场次佣金
            'view_live_data_commission',
            //是否可提交添加佣金
            'add_live_data_commission',
            //是否可提交退佣金
            'refund_live_data_commission',
            //是否可刷新罗盘数据
            'refresh_live_data_luopan_detail',
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('基本信息')
                    ->schema([
                        Forms\Components\Grid::make(4)
                            ->schema([
                                Forms\Components\Select::make('anchor_id')
                                    ->label('所属主播')
                                    ->relationship('anchor', 'real_name')
                                    ->required()
                                    ->default(0),
                                Forms\Components\Select::make('anchor_account_id')
                                    ->label('所属账号')
                                    ->relationship('anchorAccount', 'account_name')
                                    ->required()
                                    ->default(0),
                                Forms\Components\DateTimePicker::make('start_time')
                                    ->label('开始时间')
                                    ->required(),
                                Forms\Components\DateTimePicker::make('end_time')
                                    ->label('结束时间')
                                    ->required(),
                                Forms\Components\TextInput::make('base_info')
                                    ->label('直播标题')
                                    ->required()
                                    ->maxLength(255)
                                    ->columnSpan(2),
                                Forms\Components\TextInput::make('room_id')
                                    ->label('场次ID')
                                    ->disabled()
                                    ->required()
                                    ->maxLength(255),
                                Forms\Components\TextInput::make('content_quality')
                                    ->label('内容质量')
                                    ->disabled()
                                    ->required()
                                    ->maxLength(255),
                            ]),
                    ]),

                Forms\Components\Section::make('佣金信息')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('total_commission')
                                    ->label('累计佣金')
                                    ->required()
                                    ->numeric()
                                    ->disabled()
                                    ->default(0.00),
                                Forms\Components\TextInput::make('returned_commission')
                                    ->label('累计已退佣')
                                    ->required()
                                    ->numeric()
                                    ->disabled()
                                    ->default(0.00),
                                Forms\Components\TextInput::make('real_commission')
                                    ->label('实际佣金')
                                    ->required()
                                    ->numeric()
                                    ->disabled()
                                    ->default(0.00),
                            ]),
                    ]),

                Forms\Components\Section::make('直播数据')
                    ->schema([
                        Forms\Components\Grid::make(4)
                            ->schema([
                                Forms\Components\TextInput::make('viewers')
                                    ->label('观看次数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('exposure')
                                    ->label('商品曝光次数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('clicks')
                                    ->label('商品点击次数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('sales')
                                    ->label('成交金额')
                                    ->required()
                                    ->numeric()
                                    ->default(0.00),
                            ]),
                    ]),

                Forms\Components\Section::make('高级统计数据')
                    ->schema([
                        Forms\Components\Grid::make(4)
                            ->schema([
                                Forms\Components\TextInput::make('avg_watch_duration')
                                    ->label('平均观看时长(秒)')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('fans_club_ucnt')
                                    ->label('新增直播团人数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('gmv')
                                    ->label('客单价(分)')
                                    ->required()
                                    ->numeric()
                                    ->default(0.00),
                                Forms\Components\TextInput::make('gpm')
                                    ->label('客单价(分)')
                                    ->required()
                                    ->numeric()
                                    ->default(0.00),
                                Forms\Components\TextInput::make('incr_fans_cnt')
                                    ->label('新增粉丝数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('online_user_cnt')
                                    ->label('平均在线人数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('online_user_ucnt')
                                    ->label('累计在线人数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('pay_cnt')
                                    ->label('成交件数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('pay_fans_ratio')
                                    ->label('成交粉丝占比')
                                    ->required()
                                    ->numeric()
                                    ->default(0.0000),
                                Forms\Components\TextInput::make('pay_ucnt')
                                    ->label('成交人数')
                                    ->required()
                                    ->numeric()
                                    ->default(0),
                                Forms\Components\TextInput::make('product_click_to_pay_rate')
                                    ->label('点击-成交转化率')
                                    ->required()
                                    ->numeric()
                                    ->default(0.0000),
                            ]),
                    ]),

                Forms\Components\Section::make('流量分析')
                    ->schema([
                        Forms\Components\Grid::make(1)
                            ->schema([
                                Forms\Components\Textarea::make('flow_order_source')
                                    ->label('流量分析数据')
                                    ->rows(3),
                            ]),
                    ]),

                Forms\Components\Section::make('备注信息')
                    ->schema([
                        Forms\Components\Grid::make(1)
                            ->schema([
                                Forms\Components\Textarea::make('remark')
                                    ->label('备注信息')
                                    ->rows(3),
                            ]),
                    ]),
            ]);
    }


    public static function table(Table $table): Table
    {
        return $table
            ->columns([

                Tables\Columns\TextColumn::make('anchor_info')
                    ->label('主播信息')
                    ->getStateUsing(function (LiveData $record) {
                        $html = "";
                        $html .= '<div><b>助理: ' . $record->anchor->user->name . '</b></div>';
                        $html .= '<div>主播: ' . $record->anchor->real_name . '</div>';
                        $html .= '<div>账号: ' . $record->anchorAccount->account_name . '</div>';
                        return $html;
                    })
                    ->html()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('anchor.user', fn($q) => $q->where('name', 'like', "%{$search}%"))
                            ->orWhereHas('anchor', fn($q) => $q->where('real_name', 'like', "%{$search}%"))
                            ->orWhereHas('anchorAccount', fn($q) => $q->where('account_name', 'like', "%{$search}%"));
                    }),
                Tables\Columns\TextColumn::make('anchor.user.name')
                    ->label('所属助理')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('anchor.real_name')
                    ->label('主播姓名')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('anchorAccount.account_name')
                    ->label('主播账号')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('duration')
                    ->label('直播时段')
                    ->getStateUsing(function (LiveData $record) {
                        $html = "";

                        $startTime = \Carbon\Carbon::parse($record->start_time); //datatime类型
                        $endTime = \Carbon\Carbon::parse($record->end_time); //datatime类型
                        $durationInSeconds = $startTime->diffInSeconds($endTime);
                        $hours = floor($durationInSeconds / 3600);
                        $minutes = floor(($durationInSeconds % 3600) / 60);
                        $seconds = $durationInSeconds % 60;

                        $html .= '<div><b>' . $record->base_info . '</b></div>';
                        $html .= "<div>直播时长: $hours 小时 $minutes 分 $seconds 秒</div>";

                        $html .= '<div>开始时间: ' . $record->start_time . '</div>';
                        $html .= '<div>结束时间: ' . $record->end_time . '</div>';
                        return $html;
                    })
                    ->html(),

                Tables\Columns\TextColumn::make('total_commission')
                    ->label('累计佣金')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->hidden(
                        //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                        !(static::can('view_live_data_commission'))
                    )
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric(
                                decimalPlaces: 2,
                                thousandsSeparator: ',',
                            )
                    ]),

                Tables\Columns\TextColumn::make('returned_commission')
                    ->label('已退佣金')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->hidden(
                        //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                        !(static::can('view_live_data_commission'))
                    )
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric(
                                decimalPlaces: 2,
                                thousandsSeparator: ',',
                            )
                    ]),

                Tables\Columns\TextColumn::make('real_commission')
                    ->label('实际佣金')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->hidden(
                        //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                        !(static::can('view_live_data_commission'))
                    )
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric(
                                decimalPlaces: 2,
                                thousandsSeparator: ',',
                            )
                    ]),


                Tables\Columns\TextColumn::make('about_commission')
                    ->label('佣金信息概览')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->getStateUsing(function (LiveData $record) {
                        $html = "";
                        $html .= '<div>累计佣金: ' . ($record->total_commission ?? 0) . '</div>';
                        $html .= '<div>累计已退佣: ' . ($record->total_refund_commission ?? 0) . '</div>';
                        $html .= '<div>实际佣金: ' . ($record->actual_commission ?? 0) . '</div>';
                        // $html .= '<div>累计已结算: ' . ($record->total_payment_commission ?? 0) . '</div>';
                        return $html;
                    })
                    ->hidden(
                        //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                        !(static::can('view_live_data_commission'))
                    )
                    ->html(),

                Tables\Columns\TextColumn::make('sales')
                    ->label('商品成交额')
                    ->numeric()
                    ->sortable()
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric(
                                decimalPlaces: 2,
                                thousandsSeparator: ',',
                            )
                    ]),


                Tables\Columns\TextColumn::make('base_info')
                    ->label('直播标题')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('viewers')
                    ->label('观看次数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),
                Tables\Columns\TextColumn::make('exposure')
                    ->label('商品曝光次数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),
                Tables\Columns\TextColumn::make('clicks')
                    ->label('商品点击次数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),
                Tables\Columns\TextColumn::make('sales')
                    ->label('成交金额')
                    ->numeric()
                    ->sortable()
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),
                Tables\Columns\TextColumn::make('content_quality')
                    ->label('内容质量')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('avg_watch_duration')
                    ->label('平均观看时长(秒)')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Average::make()
                            ->label('平均')
                            ->numeric(),
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('fans_club_ucnt')
                    ->label('新增直播团人数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('gmv')
                    ->label('客单价(分)')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Average::make()
                            ->label('平均')
                            ->numeric(),
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('gpm')
                    ->label('客单价(分)')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Average::make()
                            ->label('平均')
                            ->numeric(),
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('incr_fans_cnt')
                    ->label('新增粉丝数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('online_user_cnt')
                    ->label('平均在线人数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Average::make()
                            ->label('平均')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('online_user_ucnt')
                    ->label('累计在线人数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('pay_cnt')
                    ->label('成交件数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('pay_fans_ratio')
                    ->label('成交粉丝占比')
                    ->numeric(
                        decimalPlaces: 4,
                        thousandsSeparator: ',',
                    )
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Average::make()
                            ->label('平均')
                            ->numeric(
                                decimalPlaces: 4,
                                thousandsSeparator: ',',
                            )
                    ]),

                Tables\Columns\TextColumn::make('pay_ucnt')
                    ->label('成交人数')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->numeric()
                    ]),

                Tables\Columns\TextColumn::make('product_click_to_pay_rate')
                    ->label('点击-成交转化率')
                    ->numeric(
                        decimalPlaces: 4,
                        thousandsSeparator: ',',
                    )
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->summarize([
                        Tables\Columns\Summarizers\Average::make()
                            ->label('平均')
                            ->numeric(
                                decimalPlaces: 4,
                                thousandsSeparator: ',',
                            )
                    ]),

                Tables\Columns\TextColumn::make('flow_order_source')
                    ->label('流量分析数据')
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('更新时间')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

            ])
            ->defaultSort('start_time', 'desc')
            ->filters([
                // 定义过滤器
                //直播标题 搜索
                Tables\Filters\Filter::make('base_info')
                    ->label('直播标题')
                    ->form([
                        Forms\Components\TextInput::make('base_info')
                            ->label('直播标题')
                            ->placeholder('请输入直播标题')
                            ->required(),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->where('base_info', 'like', "%{$data['base_info']}%");
                    },
                    ),
                //定义主播用户
                Tables\Filters\SelectFilter::make('anchor_id')
                    ->label('所属主播')
                    ->options(\App\Models\Anchor::all()->pluck('real_name', 'id'))
                    ->query(function (Builder $query, array $data): Builder {
                        $id = $data['value'];
                        if (empty($id)) {
                            return $query;
                        } else {
                            $anchor_account_ids= \App\Models\AnchorAccount::where('anchor_id', $id)->pluck('id');
                            return $query->whereIn('anchor_account_id', $anchor_account_ids);
                            // 直接获取直播数据表的所属主播列
                            // return $query->where('anchor_id', $id);
                        }
                    }),
                Tables\Filters\SelectFilter::make('anchor_account_id')
                    ->label('所属主播账号')
                    ->options(\App\Models\AnchorAccount::all()->pluck('account_name', 'id'))
                    ->query(function (Builder $query, array $data): Builder {
                        $id = $data['value'];
                        if (empty($id)) {
                            return $query;
                        } else {
                            return $query->where('anchor_account_id', $id);
                        }
                    }),
                Tables\Filters\Filter::make('date_range')
                    ->label('直播时间筛选')
                    ->form([
                        Forms\Components\Select::make('preset')
                            ->label('开播时间')
                            ->options([
                                'today' => '今日直播',
                                'yesterday' => '昨日直播',
                                'this_week' => '本周直播',
                                'this_month' => '本月直播',
                                'custom' => '自定义时间段',
                            ])
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === null) {
                                    // 当取消选择时，清空日期
                                    $set('start_date', null);
                                    $set('end_date', null);
                                } elseif ($state === 'today') {
                                    $set('start_date', today());
                                    $set('end_date', today());
                                } elseif ($state === 'yesterday') {
                                    $set('start_date', today()->subDay());
                                    $set('end_date', today()->subDay());
                                } elseif ($state === 'this_week') {
                                    $set('start_date', now()->startOfWeek());
                                    $set('end_date', now()->endOfWeek());
                                } elseif ($state === 'this_month') {
                                    $set('start_date', now()->startOfMonth());
                                    $set('end_date', now()->endOfMonth());
                                }
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('开始日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === null) {
                                    $set('preset', null);
                                }
                            })
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('结束日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === null) {
                                    $set('preset', null);
                                }
                            })
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // 如果没有选择预设且没有自定义日期，则返回原始查询
                        if (empty($data['preset']) && empty($data['start_date']) && empty($data['end_date'])) {
                            return $query;
                        }

                        return $query
                            ->when(
                                $data['start_date'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('start_time', '>=', $date),
                            )
                            ->when(
                                $data['end_date'] ?? null,
                                fn (Builder $query, $date): Builder => $query->whereDate('start_time', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        // 如果没有选择预设且没有自定义日期，则不显示指示器
                        if (empty($data['preset']) && empty($data['start_date']) && empty($data['end_date'])) {
                            return null;
                        }

                        if ($data['preset'] === 'custom') {
                            if (! $data['start_date'] && ! $data['end_date']) {
                                return null;
                            }

                            return '直播时间: ' .
                                Carbon::parse($data['start_date'])->format('Y-m-d') .
                                ' 至 ' .
                                Carbon::parse($data['end_date'])->format('Y-m-d');
                        }

                        return match ($data['preset']) {
                            'today' => '今日直播',
                            'yesterday' => '昨日直播',
                            'this_week' => '本周直播',
                            'this_month' => '本月直播',
                            default => null
                        };
                    }),
            ], layout: \Filament\Tables\Enums\FiltersLayout::AboveContent)
            // //是否等待用户点击了 应用 才进行筛选
            // ->deferFilters()
            //定义过滤器可能占用的列数
            ->filtersFormColumns(4)
            //控制筛选器下拉列表的宽度
            ->filtersFormWidth(\Filament\Support\Enums\MaxWidth::FourExtraLarge)
            //控制好过滤器的下拉列表最大高度
            ->filtersFormMaxHeight("400px")
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()->disabled(
                    fn(LiveData $record) => $record->is_completed_lock
                ),

                Tables\Actions\ActionGroup::make([
                    LiveDataAction::actionCommission("Tables","add_commission")
                        ->hidden(
                            //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                            !(static::can('add_live_data_commission'))
                        ),
                    LiveDataAction::actionCommission("Tables","refund_commission")
                        ->hidden(
                            //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                            !(static::can('refund_live_data_commission'))
                        ),
                    // LiveDataAction::actionCommission("Tables","payment_commission"),

                    LiveDataAction::actionGetLivedataDetail("Tables","get_livedata_detail","获取高级统计数据")
                    ->label('刷新罗盘数据')
                    ->hidden(
                        //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                        !(static::can('refresh_live_data_luopan_detail'))
                    )
                    ->disabled(function(LiveData $record) {
                        // 获取当前场次对应的主播账号的cookies是否有在线的.
                        $anchor_account = \App\Models\AnchorAccount::where('id', $record->anchor_account_id)->first();
                        if (!$anchor_account) {
                            return true;
                        }
                        // 判断cookies是否在线
                        $cookies_online = $anchor_account->cookies_latest_online_first();
                        if (!$cookies_online) {
                            return true;
                        }
                        return false;
                    }),
                ])
                    ->label('佣金操作'),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
                Tables\Actions\ExportBulkAction::make()
                    ->label('导出数据')
                    ->exporter(\App\Filament\Exports\LiveDataExporter::class)
            ]);
    }


    public static function getRelations(): array
    {
        return [
            RelationManagers\LiveDataCommissionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListLiveData::route('/'),
            'create' => Pages\CreateLiveData::route('/create'),
            'edit'   => Pages\EditLiveData::route('/{record}/edit'),
            'view'   => Pages\ViewLiveData::route('/{record}'),
        ];
    }
}
