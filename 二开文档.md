# DYMCN 抖音数据看板系统 - 二次开发文档

## 项目概述

DYMCN 是一个基于 Laravel 11 框架开发的抖音 MCN 数据管理系统，主要用于管理主播信息、直播数据、佣金统计等业务功能。系统采用现代化的技术栈，提供了完整的后台管理界面和数据可视化大屏展示功能。

## 技术栈分析

### 后端技术栈
- **PHP**: ^8.2
- **Laravel Framework**: ^11.0
- **Filament**: ^3.2 (现代化的 Laravel 管理面板)
- **Laravel Octane**: ^2.5 (高性能应用服务器)
- **Laravel Horizon**: ^5.25 (队列监控)
- **Spatie Laravel Settings**: 系统设置管理
- **Spatie Activity Log**: 操作日志记录
- **Laravel Excel**: Excel 导入导出
- **Chrome PHP**: 浏览器自动化操作
- **WebDriver**: Selenium WebDriver PHP 绑定

### 前端技术栈
- **Vite**: ^5.0 (现代化构建工具)
- **Laravel Vite Plugin**: ^1.0
- **Axios**: ^1.6.4 (HTTP 客户端)
- **Livewire**: 实时交互组件
- **Filament UI**: 基于 Tailwind CSS 的管理界面

### 数据库
- **MySQL**: 主数据库
- **Redis**: 缓存和队列存储
- **SQLite**: 开发环境数据库

### 部署和性能
- **FrankenPHP**: 高性能 PHP 应用服务器
- **RoadRunner**: 可选的高性能应用服务器

## 项目目录结构

```
├── app/
│   ├── Console/           # Artisan 命令
│   ├── Filament/          # Filament 管理面板相关
│   │   ├── Actions/       # 自定义操作
│   │   ├── Exports/       # 数据导出器
│   │   ├── Pages/         # 自定义页面
│   │   ├── Resources/     # 资源管理器
│   │   ├── Tables/        # 表格组件
│   │   └── Widgets/       # 仪表板小部件
│   ├── Http/
│   │   └── Controllers/   # 控制器
│   ├── Jobs/              # 队列任务
│   ├── Livewire/          # Livewire 组件
│   ├── Models/            # Eloquent 模型
│   ├── Policies/          # 授权策略
│   ├── Providers/         # 服务提供者
│   ├── Services/          # 业务服务类
│   ├── Settings/          # 系统设置
│   └── Tables/            # 表格相关
├── config/                # 配置文件
├── database/
│   ├── migrations/        # 数据库迁移
│   ├── seeders/           # 数据填充
│   └── factories/         # 模型工厂
├── resources/
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript 文件
│   └── views/             # Blade 模板
├── routes/                # 路由定义
└── storage/               # 存储目录
```

## 核心业务模块

### 1. 主播管理 (Anchor)
- **模型**: `App\Models\Anchor`
- **资源**: `App\Filament\Resources\AnchorResource`
- **功能**: 管理主播基本信息、佣金统计、关联账号

**主要字段**:
- `real_name`: 主播姓名
- `phone`: 电话号码
- `id_card`: 身份证号码
- `total_commission`: 累计佣金
- `returned_commission`: 累计已退佣
- `real_commission`: 实际佣金
- `payment_commission`: 累计已结算

### 2. 主播账号管理 (AnchorAccount)
- **模型**: `App\Models\AnchorAccount`
- **资源**: `App\Filament\Resources\AnchorAccountResource`
- **功能**: 管理主播的抖音账号信息

### 3. 直播数据管理 (LiveData)
- **模型**: `App\Models\LiveData`
- **资源**: `App\Filament\Resources\LiveDataResource`
- **功能**: 记录和管理直播数据统计

**主要字段**:
- `start_time/end_time`: 直播时间
- `viewers`: 观看人数
- `exposure`: 曝光量
- `clicks`: 点击量
- `sales`: 销售额
- `content_quality`: 内容质量

### 4. 佣金管理
- **佣金日志**: `App\Models\AnchorCommissionLog`
- **佣金统计**: `App\Models\AnchorCommissionStat`
- **直播佣金**: `App\Models\LiveDataCommission`

### 5. Cookie 管理 (Cookies)
- **模型**: `App\Models\Cookies`
- **功能**: 管理抖音登录 Cookie，用于数据抓取

## API 接口

### 大屏数据 API
系统提供了完整的大屏展示 API，支持跨域访问：

- `GET /large_screen_api/config` - 获取配置信息
- `GET /large_screen_api/today_chart_data` - 今日图表数据
- `GET /large_screen_api/month_chart_data` - 月度图表数据
- `GET /large_screen_api/today_rank_list` - 今日排行榜
- `GET /large_screen_api/week_rank_list` - 周排行榜
- `GET /large_screen_api/month_rank_list` - 月排行榜

### Cookie 管理 API
- `GET/POST /addons/dymcn/cookies` - Cookie 管理接口

## 权限管理

系统使用 Filament Shield 进行权限管理：
- 基于角色的访问控制 (RBAC)
- 细粒度的资源权限控制
- 支持数据级别的权限隔离

## 队列任务

### 主要任务类
- `CheckCookiesIsOnlineJob`: 检查 Cookie 在线状态
- `CheckLiveDataJob`: 检查直播数据

## 服务类

### 核心服务
- `BuyinService`: 购买相关服务
- `BuyinByWebViewService`: WebView 购买服务
- `CookiesService`: Cookie 管理服务
- `CookiesByWebViewService`: WebView Cookie 服务

## 数据抓取机制

系统使用以下技术进行数据抓取：
- **Chrome PHP**: 控制 Chrome 浏览器
- **WebDriver**: Selenium WebDriver 自动化
- **Cookie 管理**: 维护登录状态

## 开发环境配置

### 环境要求
- PHP >= 8.2
- MySQL >= 8.0
- Redis
- Node.js >= 18
- Chrome/Chromium 浏览器

### 安装步骤
1. 克隆项目并安装依赖
```bash
composer install
npm install
```

2. 配置环境变量
```bash
cp .env.example .env
php artisan key:generate
```

3. 数据库迁移
```bash
php artisan migrate
php artisan db:seed
```

4. 构建前端资源
```bash
npm run build
```

5. 启动服务
```bash
php artisan octane:start --server=frankenphp
```

## 二次开发指南

### 1. 添加新的业务模块

#### 创建模型
```bash
php artisan make:model NewModel -m
```

#### 创建 Filament 资源
```bash
php artisan make:filament-resource NewModel --generate
```

### 2. 扩展现有功能

#### 添加新字段
1. 创建数据库迁移
2. 更新模型的 `$fillable` 属性
3. 在 Filament 资源中添加表单字段和表格列

#### 自定义操作
在 `app/Filament/Actions/` 目录下创建自定义操作类

### 3. API 开发

#### 添加新的 API 端点
1. 在 `routes/web.php` 中定义路由
2. 创建对应的控制器方法
3. 添加跨域中间件（如需要）

### 4. 队列任务开发

#### 创建新任务
```bash
php artisan make:job NewJob
```

#### 调度任务
在 `app/Console/Kernel.php` 中配置定时任务

### 5. 前端开发

#### 自定义样式
在 `resources/css/app.css` 中添加自定义样式

#### JavaScript 功能
在 `resources/js/app.js` 中添加自定义脚本

## 部署建议

### 生产环境优化
1. 启用 Octane 或 RoadRunner
2. 配置 Redis 缓存
3. 启用队列处理
4. 配置日志轮转
5. 设置定时任务

### 安全建议
1. 定期更新依赖包
2. 配置防火墙规则
3. 启用 HTTPS
4. 定期备份数据库
5. 监控系统日志

## 常见问题

### 1. Cookie 失效问题
- 检查 Cookie 有效期
- 重新获取登录 Cookie
- 验证账号状态

### 2. 数据抓取失败
- 检查网络连接
- 验证 Chrome 浏览器配置
- 检查目标网站反爬机制

### 3. 权限问题
- 检查用户角色配置
- 验证权限分配
- 清除权限缓存

## 技术支持

如需技术支持或有疑问，请参考：
- Laravel 官方文档
- Filament 官方文档
- 项目 GitHub Issues

---

*本文档基于项目当前版本编写，如有更新请及时同步文档内容。*
