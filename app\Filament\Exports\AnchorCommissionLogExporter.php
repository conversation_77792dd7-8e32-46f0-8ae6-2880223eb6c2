<?php

namespace App\Filament\Exports;

use App\Models\AnchorCommissionLog;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;

class AnchorCommissionLogExporter extends Exporter
{
    protected static ?string $model = AnchorCommissionLog::class;

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('anchor.real_name')
                ->label('主播姓名'),

            ExportColumn::make('anchorAccount.account_name')
                ->label('主播账号'),

            ExportColumn::make('liveData.base_info')
                ->label('直播场次'),

            ExportColumn::make('change_type')
                ->label('变动类型')
                ->formatStateUsing(fn (string $state): string => AnchorCommissionLog::getChangeTypeList()[$state] ?? '未知'),

            ExportColumn::make('change_amount')
                ->label('变动金额'),

            ExportColumn::make('operator')
                ->label('操作人'),

            ExportColumn::make('created_at')
                ->label('变动时间')
                ->formatStateUsing(fn ($state) => $state->format('Y-m-d H:i:s')),

            ExportColumn::make('remark')
                ->label('备注信息'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $count = number_format($export->successful_rows);
        return "已成功导出 {$count} 条主播佣金明细";
    }

    public function getFileName(Export $export): string
    {
        return "主播佣金明细_{$export->created_at->format('Ymd_His')}.csv";
    }
}
