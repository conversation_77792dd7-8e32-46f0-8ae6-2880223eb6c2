<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 主播表: 真实姓名,身份证,电话号码,累计已结算佣金,累计已退佣金,累计佣金,备注信息
     */
    public function up(): void
    {
        Schema::create('anchor', function (Blueprint $table) {
            $table->id();
            $table->string('real_name')->comment('真实姓名');
            $table->string('id_card')->comment('身份证');
            $table->string('phone')->comment('电话号码');
            $table->decimal('total_commission', 10, 2)->default(0)->comment('累计佣金');
            $table->decimal('returned_commission', 10, 2)->default(0)->comment('累计已退佣');
            $table->decimal('real_commission', 10, 2)->default(0)->comment('实际佣金');
            // $table->decimal('payment_commission', 10, 2)->default(0)->comment('累计已结算');
            $table->text('remark')->nullable()->comment('备注信息');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('anchor');
    }
};
