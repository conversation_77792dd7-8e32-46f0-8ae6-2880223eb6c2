<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LiveDataCommissionResource\Pages;
use App\Models\LiveDataCommission;
use App\Models\User;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Permission\Models\Permission;

class LiveDataCommissionResource extends Resource implements HasShieldPermissions
{

    protected static ?int $navigationSort = 50;

    protected static ?string $model = LiveDataCommission::class;


    public static ?string $label = '直播佣金';

    protected static ?string $navigationLabel = '直播佣金列表';

    protected static ?string $navigationGroup = 'MCN管理';

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function getPermissionPrefixes(): array
    {
        return array_merge(\BezhanSalleh\FilamentShield\Support\Utils::getGeneralResourcePermissionPrefixes(), [
            //显示任意用户数据功能
            'view_any_user_data',
            //是否可审批佣金记录
            'approve_live_data_commission',
            //是否可以查看佣金所属场次详情
            'view_live_data_commission_detail',
        ]);
    }

    public static function getEloquentQuery(): Builder
    {
        /** @var User $user */
        $user = Auth::user();

        //如果是超级管理员,则返回所有
        if ($user->isSuperAdmin()) {
            return parent::getEloquentQuery();
        } else {
            if (static::can('view_any_user_data')) {
                return parent::getEloquentQuery();
            } else {
                //获取当前用户的数据
                $liveDataCommissions_ids = $user->liveDataCommissions()->pluck('dymcn_livedata_commission.id')->toArray();
                return parent::getEloquentQuery()->whereIn('id', $liveDataCommissions_ids);
            }
        }
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('申请信息')
                    ->schema([
                        Forms\Components\Select::make('livedata_id')
                            ->label('直播场次')
                            ->relationship('liveData')
                            ->columnSpanFull()
                            ->getOptionLabelFromRecordUsing(fn(Model $record) => " {$record->start_time} - {$record->end_time} {$record->base_info} 成交金额:{$record->sales}")
                            ->disabled(),

                        Forms\Components\TextInput::make('req_username')
                            ->label('申请人')
                            ->disabled(),

                        Forms\Components\Select::make('req_type')
                            ->label('佣金类型')
                            ->options(LiveDataCommission::getReqTypeList())
                            ->disabled(),


                        Forms\Components\TextInput::make('req_amount')
                            ->label('佣金金额')
                            ->numeric()
                            ->disabled(),

                        Forms\Components\Textarea::make('req_remark')
                            ->label('申请备注')
                            ->disabled()
                            //宽度占比
                            ->columnSpan(2),

                        Forms\Components\ViewField::make('req_images')
                            ->label('相关图片')
                            ->view('filament.components.image-grid')
                            ->columnSpan(1)
                            ->hidden(fn (LiveDataCommission $record): bool => empty($record->req_images)),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('审批信息')
                    ->schema([
                        Forms\Components\TextInput::make('approval_status')
                            ->label('审批状态')
                            ->formatStateUsing(fn ($state) => LiveDataCommission::getApprovalStatusList()[$state] ?? $state)
                            ->disabled(),

                        Forms\Components\TextInput::make('approver_name')
                            ->label('审批人')
                            ->disabled()
                            ->visible(fn ($record) => !empty($record?->approver_name)),

                        Forms\Components\Textarea::make('approval_remark')
                            ->label('审批备注')
                            ->disabled()
                            ->columnSpanFull()
                            ->visible(fn ($record) => !empty($record?->approval_remark)),
                    ])
                    ->columns(2)
                    ->visible(fn ($record) => $record?->approval_status !== 'pending'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return self::daya_table($table, 'main');
    }


    public static function daya_table(Table $table, $daya_type = 'main'): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('approval_status')
                    ->label('审批状态')
                    ->formatStateUsing(fn ($state) => LiveDataCommission::getApprovalStatusList()[$state] ?? $state)
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'warning',
                        'approved' => 'success',
                        'rejected' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('req_username')
                    ->label('申请人')
                    ->searchable(),
                Tables\Columns\TextColumn::make('anchor_info')
                    ->label('主播信息')
                    ->getStateUsing(function (LiveDataCommission $record) {
                        $html = "";
                        $html .= '<div><b>助理: ' . $record->liveData->anchor->user->name . '</b></div>';
                        $html .= '<div>主播: ' . $record->liveData->anchor->real_name . '</div>';
                        $html .= '<div>账号: ' . $record->liveData->anchorAccount->account_name . '</div>';
                        return $html;
                    })
                    ->html()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('anchor.user', fn($q) => $q->where('name', 'like', "%{$search}%"))
                            ->orWhereHas('anchor', fn($q) => $q->where('real_name', 'like', "%{$search}%"))
                            ->orWhereHas('anchorAccount', fn($q) => $q->where('account_name', 'like', "%{$search}%"));
                    }),

                Tables\Columns\TextColumn::make('liveData.anchor.user.name')
                    ->label('所属助理')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('liveData.anchor.real_name')
                    ->label('主播姓名')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('liveData.anchorAccount.account_name')
                    ->label('主播账号')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('livedata.base_info')
                    ->label('直播场次')
                    ->getStateUsing(function ($record) {
                        $html = "";
                        $html .= '<div><b>' . $record->liveData->base_info . '</b></div>';
                        $html .= '<div>开始时间: ' . $record->liveData->start_time . '</div>';
                        $html .= '<div>结束时间: ' . $record->liveData->end_time . '</div>';
                        $html .= '<div>成交金额: ' . $record->liveData->sales . '</div>';
                        return $html;
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('req_type')
                    ->label('佣金类型')
                    ->formatStateUsing(function ($state) {
                        $reqTypeList = LiveDataCommission::getReqTypeList();
                        if (isset($reqTypeList[$state])) {
                            $reqType = $reqTypeList[$state];
                        } else {
                            $reqType = '不支持的类型';
                        }
                        return $reqType;
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('req_amount')
                    ->label('佣金金额')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                // 主播筛选
                Tables\Filters\SelectFilter::make('anchor_id')
                    ->label('所属主播')
                    ->options(\App\Models\Anchor::all()->pluck('real_name', 'id'))
                    ->query(function (Builder $query, array $data): Builder {
                        $id = $data['value'];
                        if (empty($id)) {
                            return $query;
                        } else {
                            // 通过主播ID获取其所有账号ID
                            $anchor_account_ids = \App\Models\AnchorAccount::where('anchor_id', $id)->pluck('id');
                            // 通过账号ID获取所有直播场次ID
                            $livedata_ids = \App\Models\LiveData::whereIn('anchor_account_id', $anchor_account_ids)->pluck('id');
                            // 最后筛选佣金记录
                            return $query->whereIn('livedata_id', $livedata_ids);
                        }
                    }),

                // 主播账号筛选
                Tables\Filters\SelectFilter::make('anchor_account_id')
                    ->label('所属主播账号')
                    ->options(\App\Models\AnchorAccount::all()->pluck('account_name', 'id'))
                    ->query(function (Builder $query, array $data): Builder {
                        $id = $data['value'];
                        if (empty($id)) {
                            return $query;
                        } else {
                            // 通过账号ID获取所有直播场次ID
                            $livedata_ids = \App\Models\LiveData::where('anchor_account_id', $id)->pluck('id');
                            // 筛选佣金记录
                            return $query->whereIn('livedata_id', $livedata_ids);
                        }
                    }),

                // 直播时间筛选
                Tables\Filters\Filter::make('live_date_range')
                    ->label('直播时间筛选')
                    ->form([
                        Forms\Components\Select::make('preset')
                            ->label('开播时间')
                            ->options([
                                'today' => '今日直播',
                                'yesterday' => '昨日直播',
                                'this_week' => '本周直播',
                                'this_month' => '本月直播',
                                'custom' => '自定义时间段',
                            ])
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === null) {
                                    // 当取消选择时，清空日期
                                    $set('start_date', null);
                                    $set('end_date', null);
                                } elseif ($state === 'today') {
                                    $set('start_date', today());
                                    $set('end_date', today());
                                } elseif ($state === 'yesterday') {
                                    $set('start_date', today()->subDay());
                                    $set('end_date', today()->subDay());
                                } elseif ($state === 'this_week') {
                                    $set('start_date', now()->startOfWeek());
                                    $set('end_date', now()->endOfWeek());
                                } elseif ($state === 'this_month') {
                                    $set('start_date', now()->startOfMonth());
                                    $set('end_date', now()->endOfMonth());
                                }
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('开始日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === null) {
                                    $set('preset', null);
                                }
                            })
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('结束日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === null) {
                                    $set('preset', null);
                                }
                            })
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // 如果没有选择预设且没有自定义日期，则返回原始查询
                        if (empty($data['preset']) && empty($data['start_date']) && empty($data['end_date'])) {
                            return $query;
                        }

                        return $query->when(
                            $data['start_date'] ?? null,
                            function (Builder $query, $date) {
                                return $query->whereHas('liveData', function ($query) use ($date) {
                                    $query->whereDate('start_time', '>=', $date);
                                });
                            }
                        )->when(
                            $data['end_date'] ?? null,
                            function (Builder $query, $date) {
                                return $query->whereHas('liveData', function ($query) use ($date) {
                                    $query->whereDate('start_time', '<=', $date);
                                });
                            }
                        );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        // 如果没有选择预设且没有自定义日期，则不显示指示器
                        if (empty($data['preset']) && empty($data['start_date']) && empty($data['end_date'])) {
                            return null;
                        }

                        if ($data['preset'] === 'custom') {
                            if (! $data['start_date'] && ! $data['end_date']) {
                                return null;
                            }

                            return '直播时间: ' .
                                Carbon::parse($data['start_date'])->format('Y-m-d') .
                                ' 至 ' .
                                Carbon::parse($data['end_date'])->format('Y-m-d');
                        }

                        return match ($data['preset']) {
                            'today' => '今日直播',
                            'yesterday' => '昨日直播',
                            'this_week' => '本周直播',
                            'this_month' => '本月直播',
                            default => null
                        };
                    }),

                // 申请时间筛选
                Tables\Filters\Filter::make('apply_date_range')
                    ->label('申请时间筛选')
                    ->form([
                        Forms\Components\Select::make('preset')
                            ->label('申请时间')
                            ->options([
                                'today' => '今日申请',
                                'yesterday' => '昨日申请',
                                'this_week' => '本周申请',
                                'this_month' => '本月申请',
                                'custom' => '自定义时间段',
                            ])
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                if ($state === 'today') {
                                    $set('start_date', today());
                                    $set('end_date', today());
                                } elseif ($state === 'yesterday') {
                                    $set('start_date', today()->subDay());
                                    $set('end_date', today()->subDay());
                                } elseif ($state === 'this_week') {
                                    $set('start_date', now()->startOfWeek());
                                    $set('end_date', now()->endOfWeek());
                                } elseif ($state === 'this_month') {
                                    $set('start_date', now()->startOfMonth());
                                    $set('end_date', now()->endOfMonth());
                                }
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('开始日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('结束日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['start_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['end_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['preset'] === 'custom') {
                            if (! $data['start_date'] && ! $data['end_date']) {
                                return null;
                            }

                            return '申请时间: ' .
                                Carbon::parse($data['start_date'])->format('Y-m-d') .
                                ' 至 ' .
                                Carbon::parse($data['end_date'])->format('Y-m-d');
                        }

                        return match ($data['preset']) {
                            'today' => '今日申请',
                            'yesterday' => '昨日申请',
                            'this_week' => '本周申请',
                            'this_month' => '本月申请',
                            default => null
                        };
                    }),

                // 佣金类筛选
                Tables\Filters\SelectFilter::make('req_type')
                    ->label('佣金类型')
                    ->options(LiveDataCommission::getReqTypeList())
                    ->query(function (Builder $query, array $data): Builder {
                        $type = $data['value'];
                        if (empty($type)) {
                            return $query;
                        } else {
                            return $query->where('req_type', $type);
                        }
                    }),

                // 申请人筛选
                Tables\Filters\Filter::make('req_username')
                    ->label('申请人')
                    ->form([
                        Forms\Components\TextInput::make('req_username')
                            ->label('申请人')
                            ->placeholder('请输入申请人姓名'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->where('req_username', 'like', "%{$data['req_username']}%");
                    }),

                // 关联直播场次筛选
                Tables\Filters\Filter::make('livedata_info')
                    ->label('直播场次标题')
                    ->form([
                        Forms\Components\TextInput::make('livedata_info')
                            ->label('直播场次标题')
                            ->placeholder('请输入直播场次标题'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // 直播场次标题不为空再筛选
                        if (!empty($data['livedata_info'])) {
                            return $query->whereHas('liveData', function ($query) use ($data) {
                                $query->where('base_info', 'like', "%{$data['livedata_info']}%");
                            });
                        }
                        return $query;
                    }),
                // 直播场次房间ID
                Tables\Filters\Filter::make('room_id')
                    ->label('直播场次房间ID')
                    ->form([
                        Forms\Components\TextInput::make('room_id')
                            ->label('直播场次房间ID')
                            ->placeholder('请输入直播场次房间ID'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        // 通过房间ID获取直播场次ID, 房间ID不为空再筛选
                        if (!empty($data['room_id'])) {
                            return $query->whereHas('liveData', function ($query) use ($data) {
                                $query->where('room_id', $data['room_id']);
                            });
                        }
                        return $query;
                    }),

            ], layout: \Filament\Tables\Enums\FiltersLayout::AboveContent)
            // 定义过滤器可能占用的列数
            ->filtersFormColumns(4)
            // 控制筛选器下拉列表的宽度
            ->filtersFormWidth(\Filament\Support\Enums\MaxWidth::FourExtraLarge)
            // 控制过滤器的下拉列表最大高度
            ->filtersFormMaxHeight("400px")
            ->actions([
                Tables\Actions\ViewAction::make(),

                // 审批按钮
                Action::make('approval')
                    ->label('审批')
                    ->icon('heroicon-o-check-circle')
                    ->color('warning')
                    ->modalHeading('佣金审批')
                    ->form([
                        Forms\Components\Section::make('申请详情')
                            ->schema([

                                Forms\Components\TextInput::make('req_username')
                                    ->label('申请人')
                                    ->default(fn (LiveDataCommission $record): string => $record->req_username)
                                    ->disabled(),
                                Forms\Components\TextInput::make('req_type')
                                    ->label('佣金类型')
                                    ->default(fn (LiveDataCommission $record): string => LiveDataCommission::getReqTypeList()[$record->req_type])
                                    ->disabled(),
                                Forms\Components\TextInput::make('req_amount')
                                    ->label('佣金金额')
                                    ->default(fn (LiveDataCommission $record): string => $record->req_amount)
                                    ->disabled(),
                                Forms\Components\Textarea::make('req_remark')
                                    ->label('申请说明')
                                    ->default(fn (LiveDataCommission $record): ?string => $record->req_remark)
                                    ->disabled()
                                    //宽度占比
                                    ->columnSpan(2),

                                Forms\Components\ViewField::make('req_images')
                                    ->label('相关图片')
                                    ->view('filament.components.image-grid')
                                    ->columnSpan(1),
                            ])
                            ->columns(3),
                        Forms\Components\Section::make('审批操作')
                            ->schema([
                                Forms\Components\Textarea::make('approval_remark')
                                    ->label('审批意见')
                                    ->rows(3),
                                Forms\Components\Radio::make('action')
                                    ->label('审批操作')
                                    ->options([
                                        'approve' => '通过审批',
                                        'reject' => '拒绝审批',
                                    ])
                                    ->descriptions([
                                        'approve' => '同意该佣金申请',
                                        'reject' => '拒绝该佣金申请',
                                    ])
                                    ->inline()
                                    ->required(),
                            ])->columns(2),
                    ])
                    ->action(function (array $data, LiveDataCommission $record): void {
                        try {
                            if ($data['action'] === 'approve') {
                                $record->approve(Auth::user()->name, $data['approval_remark']);
                                Notification::make()
                                    ->title('审批通过成功')
                                    ->success()
                                    ->send();
                            } else {
                                $record->reject(Auth::user()->name, $data['approval_remark']);
                                Notification::make()
                                    ->title('审批拒绝成功')
                                    ->success()
                                    ->send();
                            }
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('审批失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn (LiveDataCommission $record): bool =>
                        $record->approval_status === 'pending' && (
                            static::can('approve_live_data_commission')
                        )
                    ),

                //自定义按钮
                Tables\Actions\Action::make('view_commission_detail')
                    ->label('场次详情')
                    ->icon('heroicon-o-eye')
                    ->hidden(function (LiveDataCommission $record) use ($daya_type) {
                        //当daya_type为main时,不显示场次详情按钮,说明该佣金是主播自己添加的
                        //当用户没有权限查看场次详情时,不显示场次详情按钮
                        return ($daya_type == 'main' ? false : true) || (!static::can('view_live_data_commission_detail'));
                    })
                    ->url(function (LiveDataCommission $record): string {
                        return "/admin/live-datas/{$record->livedata_id}";
                    }),
            ])
            ->bulkActions([
                // 批量审批操作
                Tables\Actions\BulkAction::make('bulk_approval')
                    ->label('批量审批')
                    ->icon('heroicon-o-check-circle')
                    ->color('warning')
                    ->modalHeading('批量佣金审批')
                    ->modalDescription(fn (Collection $records) => '已选择 ' . $records->where('approval_status', 'pending')->count() . ' 条待审批记录')
                    ->form([
                        Forms\Components\Section::make('审批操作')
                            ->schema([
                                Forms\Components\Textarea::make('approval_remark')
                                    ->label('审批意见')
                                    ->rows(3),
                                Forms\Components\Radio::make('action')
                                    ->label('审批操作')
                                    ->options([
                                        'approve' => '通过审批',
                                        'reject' => '拒绝审批',
                                    ])
                                    ->descriptions([
                                        'approve' => '同意选中的佣金申请',
                                        'reject' => '拒绝选中的佣金申请',
                                    ])
                                    ->inline()
                                    ->required(),
                            ])->columns(2),
                    ])
                    ->action(function (array $data, Collection $records): void {
                        // 获取待审批的记录
                        $pendingRecords = $records->where('approval_status', 'pending');

                        if ($pendingRecords->isEmpty()) {
                            Notification::make()
                                ->title('没有可审批的记录')
                                ->warning()
                                ->send();
                            return;
                        }

                        try {
                            DB::beginTransaction();

                            foreach ($pendingRecords as $record) {
                                if ($data['action'] === 'approve') {
                                    $record->approve(Auth::user()->name, $data['approval_remark']);
                                } else {
                                    $record->reject(Auth::user()->name, $data['approval_remark']);
                                }
                            }

                            DB::commit();

                            $actionText = $data['action'] === 'approve' ? '通过' : '拒绝';
                            Notification::make()
                                ->title("批量{$actionText}成功")
                                ->body("成功{$actionText} {$pendingRecords->count()} 条记录")
                                ->success()
                                ->send();
                        } catch (\Exception $e) {
                            DB::rollBack();
                            Notification::make()
                                ->title('审批失败')
                                ->body($e->getMessage())
                                ->danger()
                                ->send();
                        }
                    })
                    ->visible(fn () => static::can('approve_live_data_commission')),

                Tables\Actions\ExportBulkAction::make()
                    ->label('导出数据')
                    ->exporter(\App\Filament\Exports\LiveDataCommissionExporter::class)
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListLiveDataCommissions::route('/'),
            'create' => Pages\CreateLiveDataCommission::route('/create'),
            // 'edit' => Pages\EditLiveDataCommission::route('/{record}/edit'),
            //自义查看页面
            // 'view' => Pages\ViewLiveDataCommission::route('/{record}/view'),
        ];
    }
}
