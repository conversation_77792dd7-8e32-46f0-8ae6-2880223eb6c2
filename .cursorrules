我是是一名使用 Laravel开发框架 的且使用 filamentphp 的全栈开发者，我和你的交互尽量使用中文交互。你在 Laravel、PHP 以及相关的 Web 开发技术方面非常精通。

核心原则

编写简洁、技术性强的回答，并附上准确的 PHP 示例。
遵循 Laravel 的最佳实践和规范。
项目使用 filamentphp 框架开发。
使用面向对象编程，注重 SOLID 原则。
更倾向于迭代和模块化，避免重复。
使用具有描述性的变量和方法名称。
目录使用小写字母并以短横线分隔（例如：app/Http/Controllers）。
优先使用依赖注入和服务容器。
PHP/Laravel

在适当情况下，使用 PHP 8.1+ 的新特性（例如：类型化属性，match 表达式）。
遵循 PSR-12 编码标准。
使用严格类型：declare(strict_types=1);
尽可能使用 Laravel 内置的功能和辅助函数。
文件结构：遵循 Laravel 的目录结构和命名规范。
实现适当的错误处理和日志记录：
使用 Laravel 的异常处理和日志功能。
必要时创建自定义异常。
对预期异常使用 try-catch 块。
使用 Laravel 的验证功能进行表单和请求验证。
使用中间件过滤和修改请求。
使用 Laravel 的 Eloquent ORM 进行数据库交互。
对于复杂的数据库查询，使用 Laravel 的查询构建器。
实现适当的数据库迁移和填充器。
依赖管理

Laravel（最新稳定版本）
使用 Composer 进行依赖管理
Laravel 最佳实践

尽可能使用 Eloquent ORM，而不是原生 SQL 查询。
实现数据访问层的仓库模式。
使用 Laravel 内置的身份验证和授权功能。
利用 Laravel 的缓存机制提升性能。
对于长时间运行的任务，使用 Laravel 的队列系统。
使用 Laravel 的测试工具（PHPUnit，Dusk）进行单元测试和功能测试。
对外公开的 API 实现版本控制。
使用 Laravel 的本地化功能支持多语言。
实现适当的 CSRF 防护和安全措施。
使用 Laravel Mix 进行资源编译。
为提高查询性能，实现适当的数据库索引。
使用 Laravel 内置的分页功能。
实现适当的错误日志记录和监控。
关键约定

遵循 Laravel 的 MVC 架构。
使用 Laravel 的路由系统定义应用程序的端点。
通过表单请求实现适当的请求验证。
使用 Laravel 的 Blade 模板引擎处理视图。
使用 Eloquent 实现正确的数据库关系。
使用 Laravel 内置的身份验证脚手架。
实现适当的 API 资源转换。
使用 Laravel 的事件和监听器系统实现解耦代码。
实现适当的数据库事务，确保数据完整性。
使用 Laravel 的内置计划任务功能处理定时任务。


## 本项目主要APP目录的文件树结构如下

```
app
├─ Console
│  └─ Commands
│     ├─ ScheduleCheckCookiesIsOnline.php
│     └─ ScheduleCheckLiveData.php
├─ Services
│  ├─ CookiesByWebViewService.php
│  ├─ CookiesService.php
│  ├─ BuyinByWebViewService.php
│  └─ BuyinService.php
├─ Listeners
│  ├─ ProcessApprovedListener.php
│  ├─ ProcessRejectedListener.php
│  └─ ProcessSubmittedListener.php
├─ Jobs
│  ├─ CheckCookiesIsOnlineJob.php
│  └─ CheckLiveDataJob.php
├─ Http
│  └─ Controllers
│     ├─ Controller.php
│     ├─ LargeScreenApiController.php
│     └─ DymcnController.php
├─ Filament
│  ├─ Widgets
│  │  └─ AccountRolesWidget.php
│  ├─ Pages
│  │  ├─ MyProfilePage.php
│  │  ├─ HealthCheckResults.php
│  │  └─ GeneralSettingsPage.php
│  ├─ Actions
│  │  ├─ AnchorAccountAction.php
│  │  ├─ CookiesAction.php
│  │  └─ LiveDataAction.php
│  └─ Resources
│     ├─ LiveDataCommissionResource.php
│     ├─ AnchorResource.php
│     ├─ UserResource.php
│     ├─ CookiesResource.php
│     ├─ AnchorAccountResource.php
│     ├─ LiveDataResource.php
│     ├─ CookiesResource
│     │  └─ Pages
│     │     ├─ ListCookies.php
│     │     ├─ EditCookies.php
│     │     └─ CreateCookies.php
│     ├─ LiveDataCommissionResource
│     │  └─ Pages
│     │     ├─ ViewLiveDataCommission.php
│     │     ├─ EditLiveDataCommission.php
│     │     ├─ ListLiveDataCommissions.php
│     │     └─ CreateLiveDataCommission.php
│     ├─ AnchorAccountResource
│     │  ├─ Pages
│     │  │  ├─ EditAnchorAccount.php
│     │  │  ├─ CreateAnchorAccount.php
│     │  │  ├─ ViewAnchorAccount.php
│     │  │  └─ ListAnchorAccounts.php
│     │  └─ RelationManagers
│     │     ├─ LiveDatasRelationManager.php
│     │     └─ CookiesRelationManager.php
│     ├─ AnchorResource
│     │  ├─ Pages
│     │  │  ├─ ListAnchors.php
│     │  │  ├─ EditAnchor.php
│     │  │  ├─ ViewAnchor.php
│     │  │  └─ CreateAnchor.php
│     │  └─ RelationManagers
│     │     └─ AnchorAccountsRelationManager.php
│     ├─ UserResource
│     │  └─ Pages
│     │     ├─ ListUsers.php
│     │     ├─ EditUser.php
│     │     └─ CreateUser.php
│     ├─ LiveDataResource
│     │  ├─ Pages
│     │  │  ├─ EditLiveData.php
│     │  │  ├─ CreateLiveData.php
│     │  │  ├─ ListLiveData.php
│     │  │  └─ ViewLiveData.php
│     │  └─ RelationManagers
│     │     └─ LiveDataCommissionsRelationManager.php
│     └─ Shield
│        ├─ RoleResource.php
│        └─ RoleResource
│           └─ Pages
│              ├─ EditRole.php
│              ├─ ViewRole.php
│              ├─ ListRoles.php
│              └─ CreateRole.php
├─ Policies
│  ├─ RolePolicy.php
│  ├─ AnchorAccountPolicy.php
│  ├─ UserPolicy.php
│  ├─ CookiesPolicy.php
│  ├─ AnchorPolicy.php
│  ├─ ProcessApprovalFlowPolicy.php
│  ├─ LiveDataPolicy.php
│  ├─ LiveDataCommissionPolicy.php
│  └─ ActivityPolicy.php
├─ Models
│  ├─ AnchorAccount.php
│  ├─ Cookies.php
│  ├─ User.php
│  ├─ Anchor.php
│  ├─ LiveDataCommission.php
│  └─ LiveData.php
├─ Providers
│  ├─ AppServiceProvider.php
│  ├─ HorizonServiceProvider.php
│  └─ Filament
│     └─ AdminPanelProvider.php
└─ Settings
   └─ GeneralSettings.php

```
本项目的主要数据模型关系如下:
主播Anchor.php,
主播有多个主播账号AnchorAccount.php,
每个主播账号有多个直播场次LiveData.php,
每个直播场次有多个直播佣金数据LiveDataCommission.php
