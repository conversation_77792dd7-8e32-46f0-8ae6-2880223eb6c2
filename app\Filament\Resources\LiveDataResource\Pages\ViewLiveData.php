<?php
namespace App\Filament\Resources\LiveDataResource\Pages;

use App\Filament\Actions\LiveDataAction;
use App\Filament\Resources\LiveDataResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewLiveData extends ViewRecord
{
    protected static string $resource = LiveDataResource::class;

    //新增按钮
    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->disabled(fn ($record) => $record->is_completed_lock),

            LiveDataAction::actionCommission("","add_commission")
                ->hidden(
                    //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                    !(static::getResource()::can('add_live_data_commission'))
                ),

            LiveDataAction::actionCommission("","refund_commission")
                ->hidden(
                    //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                    !(static::getResource()::can('refund_live_data_commission'))
                ),

            LiveDataAction::actionGetLivedataDetail("","get_livedata_detail","获取高级统计数据")
                ->label('刷新罗盘数据')
                ->hidden(
                    //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                    !(static::getResource()::can('refresh_live_data_luopan_detail'))
                )
                ->disabled(function($record) {
                    // 获取当前场次对应的主播账号的cookies是否有在线的.
                    $anchor_account = \App\Models\AnchorAccount::where('id', $record->anchor_account_id)->first();
                    if (!$anchor_account) {
                        return true;
                    }
                    // 判断cookies是否在线
                    $cookies_online = $anchor_account->cookies_latest_online_first();
                    if (!$cookies_online) {
                        return true;
                    }
                    return false;
                }),
        ];
    }
}
