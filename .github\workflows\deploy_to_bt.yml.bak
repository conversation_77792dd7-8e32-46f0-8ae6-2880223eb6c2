
name: 部署到宝塔面板

on:
  push:
    branches:
      - main


jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
        - name: 检出代码
          uses: actions/checkout@v4

        - name: 列出当前目录
          run: ls -al

        - name: 列出当前目录路径
          run: pwd

        - name: 部署到ssh
          uses: easingthemes/ssh-deploy@main
          with:
            SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
            ARGS: "-rlgoDzvc -i"
            SOURCE: "./"
            REMOTE_HOST: ${{ vars.REMOTE_HOST }}
            REMOTE_PORT: ${{ vars.REMOTE_PORT }}
            REMOTE_USER: ${{ vars.REMOTE_USER }}
            TARGET: "/www/wwwroot/dymcn-laravel.ttmm.vip/prod_web"
            EXCLUDE: "/.git/, /vendor/"
            SCRIPT_BEFORE: |
              cp -nr /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/.env /www/wwwroot/dymcn-laravel.ttmm.vip/.env
              rm -rf /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/*
            SCRIPT_AFTER: |

##将temp_web中的layout.html找到,并替换其中的 {-当前日期时间-} 为 当前日期时间
#  layout_html_path="/www/wwwroot/imei.ttmm.vip/temp_web/addons/imei/view/common/layout.html"
#  #将layout.html中的 {-当前日期时间-} 替换为当前日期时间
#  sed -i "s/{-当前日期时间-}/$(date '+%Y-%m-%d %H:%M:%S')/g" $layout_html_path
              cp -nr /www/wwwroot/dymcn-laravel.ttmm.vip/.env /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/.env
              cd /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/

              echo y | /usr/bin/php82 /usr/bin/composer  config repo.packagist composer https://mirrors.aliyun.com/composer/
              echo y | /usr/bin/php82 /usr/bin/composer update

              chown -R www:www /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/
              chmod 755 -R /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/

