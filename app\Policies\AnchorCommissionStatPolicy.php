<?php

namespace App\Policies;

use App\Models\User;
use App\Models\AnchorCommission;
use Illuminate\Auth\Access\HandlesAuthorization;

class AnchorCommissionStatPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_anchor::commission::stat');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, AnchorCommission $anchorCommission): bool
    {
        return $user->can('view_anchor::commission::stat');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_anchor::commission::stat');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, AnchorCommission $anchorCommission): bool
    {
        return $user->can('update_anchor::commission::stat');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, AnchorCommission $anchorCommission): bool
    {
        return $user->can('delete_anchor::commission::stat');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_anchor::commission::stat');
    }
}
