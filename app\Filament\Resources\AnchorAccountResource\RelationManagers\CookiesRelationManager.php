<?php

namespace App\Filament\Resources\AnchorAccountResource\RelationManagers;

use App\Filament\Resources\CookiesResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class CookiesRelationManager extends RelationManager
{
    protected static string $relationship = 'cookies';
    protected static ?string $title = 'Cookies';

    //是否只读,如果是true只读,则会隐藏创建,编辑,删除
    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return CookiesResource::form($form);
    }

    public function table(Table $table): Table
    {
        return CookiesResource::table($table);
    }
}
