<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dymcn_livedata_commission', function (Blueprint $table) {
            $table->comment('直播佣金');
            $table->bigIncrements('id');
            $table->integer('livedata_id')->default(0)->comment('所属场次ID');
            $table->string('req_type')->default('')->comment('申请类型');
            $table->string('req_username')->default('')->comment('申请人');
            $table->decimal('req_amount', 10, 2)->default(0.00)->comment('申请金额');
            $table->text('req_remark')->nullable()->comment('申请备注');
            $table->text('req_images')->nullable()->comment('申请图片');
            $table->string('approval_status')->nullable()->default('')->comment('审批状态');
            $table->string('approver_name')->nullable()->default('')->comment('审批操作人');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dymcn_livedata_commission');
    }
};
