<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AnchorCommissionLogResource\Pages;
use App\Models\AnchorCommissionLog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Carbon\Carbon;
use App\Filament\Exports\AnchorCommissionLogExporter;
use Illuminate\Support\Facades\Auth;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;

class AnchorCommissionLogResource extends Resource implements HasShieldPermissions
{
    protected static ?string $model = AnchorCommissionLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?int $navigationSort = 30;

    public static ?string $label = '主播佣金明细';

    protected static ?string $navigationLabel = '主播佣金明细';

    protected static ?string $navigationGroup = 'MCN管理';

    public static function getPermissionPrefixes(): array
    {
        return [
            'view',
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('anchor_id')
                            ->relationship('anchor', 'real_name')
                            ->label('所属主播')
                            ->searchable()
                            ->preload()
                            ->live()
                            ->required(),

                        Forms\Components\Select::make('anchor_account_id')
                            ->relationship('anchorAccount', 'account_name', function ($query, $get) {
                                $anchorId = $get('anchor_id');
                                if ($anchorId) {
                                    $query->where('anchor_id', $anchorId);
                                }
                            })
                            ->label('主播账号')
                            ->searchable()
                            ->preload()
                            ->required(),

                        Forms\Components\Hidden::make('livedata_id')
                            ->default(0),

                        Forms\Components\Select::make('change_type')
                            ->options(AnchorCommissionLog::getChangeTypeList())
                            ->label('变动类型')
                            ->required(),

                        Forms\Components\TextInput::make('change_amount')
                            ->label('变动金额')
                            ->numeric()
                            ->required()
                            ->hint('添加佣金请输入正数，退回佣金请输入正数（系统会自动转为负数）'),

                        Forms\Components\DateTimePicker::make('created_at')
                            ->label('变动时间')
                            ->native(false)
                            ->displayFormat('Y-m-d H:i:s')
                            ->default(now())
                            ->required(),

                        Forms\Components\Hidden::make('operator')
                            ->default(fn() => Auth::user()->name),

                        Forms\Components\Textarea::make('remark')
                            ->label('备注信息')
                            ->columnSpan(2),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('anchor_info')
                    ->label('主播信息')
                    ->getStateUsing(function (AnchorCommissionLog $record) {
                        $html = "";
                        $html .= '<div><b>助理: ' . $record->anchor->user->name . '</b></div>';
                        $html .= '<div>主播: ' . $record->anchor->real_name . '</div>';
                        $html .= '<div>账号: ' . $record->anchorAccount->account_name . '</div>';
                        return $html;
                    })
                    ->html()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query->whereHas('anchor.user', fn($q) => $q->where('name', 'like', "%{$search}%"))
                            ->orWhereHas('anchor', fn($q) => $q->where('real_name', 'like', "%{$search}%"))
                            ->orWhereHas('anchorAccount', fn($q) => $q->where('account_name', 'like', "%{$search}%"));
                    }),

                Tables\Columns\TextColumn::make('livedata.base_info')
                    ->label('直播场次')
                    ->searchable(),

                Tables\Columns\TextColumn::make('change_type')
                    ->label('变动类型')
                    ->formatStateUsing(fn (string $state): string => AnchorCommissionLog::getChangeTypeList()[$state] ?? '未知')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'add_commission' => 'success',
                        'refund_commission' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('change_amount')
                    ->label('变动金额')
                    ->money('CNY')
                    ->color(fn ($state): string => $state > 0 ? 'success' : 'danger')
                    ->alignRight()
                    ->sortable()
                    ->summarize([
                        Tables\Columns\Summarizers\Sum::make()
                            ->label('合计')
                            ->money('CNY'),
                    ]),

                Tables\Columns\TextColumn::make('operator')
                    ->label('操作人')
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('创建时间')
                    ->dateTime('Y-m-d H:i:s')
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                // 主播筛选
                Tables\Filters\SelectFilter::make('anchor_id')
                    ->label('所属主播')
                    ->relationship('anchor', 'real_name')
                    ->searchable()
                    ->preload(),

                // 主播账号筛选
                Tables\Filters\SelectFilter::make('anchor_account_id')
                    ->label('主播账号')
                    ->relationship('anchorAccount', 'account_name')
                    ->searchable()
                    ->preload(),

                // 变动类型筛选
                Tables\Filters\SelectFilter::make('change_type')
                    ->label('变动类型')
                    ->options(AnchorCommissionLog::getChangeTypeList()),

                // 时间范围筛选
                Tables\Filters\Filter::make('created_at')
                    ->label('变动时间')
                    ->form([
                        Forms\Components\Select::make('preset')
                            ->label('快速选择')
                            ->options([
                                'today' => '今日变动',
                                'yesterday' => '昨日变动',
                                'this_week' => '本周变动',
                                'this_month' => '本月变动',
                                'last_month' => '上月变动',
                                'custom' => '自定义时间段',
                            ])
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                if ($state === 'today') {
                                    $set('start_date', today());
                                    $set('end_date', today());
                                } elseif ($state === 'yesterday') {
                                    $set('start_date', today()->subDay());
                                    $set('end_date', today()->subDay());
                                } elseif ($state === 'this_week') {
                                    $set('start_date', today()->startOfWeek());
                                    $set('end_date', today()->endOfWeek());
                                } elseif ($state === 'this_month') {
                                    $set('start_date', today()->startOfMonth());
                                    $set('end_date', today()->endOfMonth());
                                } elseif ($state === 'last_month') {
                                    $set('start_date', today()->subMonth()->startOfMonth());
                                    $set('end_date', today()->subMonth()->endOfMonth());
                                }
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('开始日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('结束日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['start_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['end_date'],
                                fn (Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['preset'] === 'custom') {
                            if (!$data['start_date'] && !$data['end_date']) {
                                return null;
                            }

                            return '变动时间: ' .
                                Carbon::parse($data['start_date'])->format('Y-m-d') .
                                ' 至 ' .
                                Carbon::parse($data['end_date'])->format('Y-m-d');
                        }

                        return match ($data['preset']) {
                            'today' => '今日变动',
                            'yesterday' => '昨日变动',
                            'this_week' => '本周变动',
                            'this_month' => '本月变动',
                            'last_month' => '上月变动',
                            default => null
                        };
                    }),
            ], layout: Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(4)
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\ExportBulkAction::make()
                    ->label('导出数据')
                    ->exporter(AnchorCommissionLogExporter::class)
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAnchorCommissionLogs::route('/'),
            'create' => Pages\CreateAnchorCommissionLog::route('/create'),
            'view' => Pages\ViewAnchorCommissionLog::route('/{record}'),
        ];
    }
}
