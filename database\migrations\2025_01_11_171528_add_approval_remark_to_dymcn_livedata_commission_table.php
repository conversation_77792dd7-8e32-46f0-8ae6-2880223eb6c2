<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('dymcn_livedata_commission', function (Blueprint $table) {
            $table->text('approval_remark')->nullable()->after('approver_name')->comment('审批意见');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('dymcn_livedata_commission', function (Blueprint $table) {
            $table->dropColumn('approval_remark');
        });
    }
};
