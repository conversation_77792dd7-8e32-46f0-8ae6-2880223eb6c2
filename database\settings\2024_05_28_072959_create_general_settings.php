<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('general.site_name', '<PERSON><PERSON>');
        $this->migrator->add('general.site_active', true);

        $this->migrator->add('general.qq', '22707370');
        $this->migrator->add('general.weixin', 'ttmm_vip');
        $this->migrator->add('general.email', '<EMAIL>');

        $this->migrator->add('general.icp', 'ICP备2024000000号');
    }

    public function down(): void
    {
        $this->migrator->delete('general.site_name');
        $this->migrator->delete('general.site_active');

        $this->migrator->delete('general.qq');
        $this->migrator->delete('general.weixin');
        $this->migrator->delete('general.email');
        $this->migrator->delete('general.icp');
    }
};
