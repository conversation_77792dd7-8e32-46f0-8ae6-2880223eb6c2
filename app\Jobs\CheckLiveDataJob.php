<?php

namespace App\Jobs;

use App\Services\BuyinService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CheckLiveDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $account_uid;

    /**
     * 任务最大重试次数
     */
    public $tries = 1;

    /**
     * 任务可以执行的最大秒数
     */
    public $timeout = 60;

    /**
     * 重试之间的等待秒数
     */
    public $backoff = [10, 30, 60];

    /**
     * Create a new job instance.
     */
    public function __construct(string $account_uid)
    {
        $this->account_uid = $account_uid;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // 刷新账户数据
            BuyinService::refreshAccount($this->account_uid);
            echo "刷新账户数据完成 {$this->account_uid} ".PHP_EOL;

            // 刷新场次数据
            $livedata_list_count = BuyinService::refreshLivedata($this->account_uid);
            echo "刷新场次数据完成 {$this->account_uid} {$livedata_list_count}场".PHP_EOL;

            // 如果有新的直播场次，自动刷新罗盘数据
            if ($livedata_list_count > 0) {
                // 获取最近10场直播的罗盘数据
                $recent_lives = \App\Models\LiveData::where('anchor_account_id', function($query) {
                    $query->select('id')
                        ->from('dymcn_anchor_accounts')
                        ->where('account_uid', $this->account_uid)
                        ->limit(1);
                })
                ->whereNull('gmv') // 只获取还没有罗盘数据的场次
                ->orderBy('start_time', 'desc')
                ->take(10)
                ->get();

                foreach ($recent_lives as $live) {
                    try {
                        // 添加随机延迟，避免并发请求
                        $delay = rand(5, 30);
                        BuyinService::get_livedata_detail($this->account_uid, $live->room_id);
                        echo "刷新罗盘数据完成 {$this->account_uid} {$live->room_id} ".PHP_EOL;
                        sleep($delay);
                    } catch (\Exception $e) {
                        Log::error("刷新罗盘数据失败", [
                            'account_uid' => $this->account_uid,
                            'room_id' => $live->room_id,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error("刷新场次数据异常", [
                'account_uid' => $this->account_uid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 如果重试次数已达到最大值，记录错误但不再重试
            if ($this->attempts() >= $this->tries) {
                Log::error("刷新场次数据达到最大重试次数", [
                    'account_uid' => $this->account_uid
                ]);
            } else {
                throw $e; // 抛出异常触发重试
            }
        }
        echo Carbon::now()->toDateTimeString().PHP_EOL;
    }
}
