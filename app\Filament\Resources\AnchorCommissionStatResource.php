<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AnchorCommissionStatResource\Pages;
use App\Models\AnchorCommissionStat;
use Filament\Forms;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Carbon\Carbon;
use Filament\Forms\Get;
use Filament\Forms\Set;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;

class AnchorCommissionStatResource extends Resource implements HasShieldPermissions
{
    protected static ?string $model = AnchorCommissionStat::class;

    protected static ?string $navigationIcon = 'heroicon-o-calculator';

    protected static ?int $navigationSort = 20;

    public static ?string $label = '主播佣金统计';

    protected static ?string $navigationLabel = '主播佣金统计';

    protected static ?string $navigationGroup = 'MCN管理';

    protected static ?string $modelLabel = '主播佣金统计';


    public static function getPermissionPrefixes(): array
    {
        return array_merge(\BezhanSalleh\FilamentShield\Support\Utils::getGeneralResourcePermissionPrefixes(), [
            //新增的权限
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('所属助理')
                    ->sortable(),

                Tables\Columns\ImageColumn::make('avatar')
                    ->label('头像')
                    ->circular(),

                Tables\Columns\TextColumn::make('real_name')
                    ->label('主播姓名')
                    ->searchable(),

                Tables\Columns\TextColumn::make('phone')
                    ->label('电话号码')
                    ->searchable(),

                Tables\Columns\TextColumn::make('id')
                    ->label('佣金统计')
                    ->formatStateUsing(function (AnchorCommissionStat $record, Tables\Table $table) {
                        $query = $record->commissionLogs();

                        // 获取当前的筛选状态
                        $filter = $table->getFilter('created_at')?->getState();

                        if ($filter) {
                            if ($filter['preset'] === 'custom') {
                                if (!empty($filter['start_date'])) {
                                    $query->whereDate('created_at', '>=', $filter['start_date']);
                                }
                                if (!empty($filter['end_date'])) {
                                    $query->whereDate('created_at', '<=', $filter['end_date']);
                                }
                            } else {
                                $dates = match ($filter['preset']) {
                                    'today' => [now()->startOfDay(), now()->endOfDay()],
                                    'yesterday' => [now()->subDay()->startOfDay(), now()->subDay()->endOfDay()],
                                    'this_week' => [now()->startOfWeek(), now()->endOfWeek()],
                                    'this_month' => [now()->startOfMonth(), now()->endOfMonth()],
                                    'last_month' => [now()->subMonth()->startOfMonth(), now()->subMonth()->endOfMonth()],
                                    default => null,
                                };

                                if ($dates) {
                                    $query->whereBetween('created_at', $dates);
                                }
                            }
                        }

                        return $query->sum('change_amount');
                    })
                    ->description(function (Tables\Table $table) {
                        $filter = $table->getFilter('created_at')?->getState();

                        if (!$filter) {
                            return '全部时间';
                        }

                        if ($filter['preset'] === 'custom') {
                            if (empty($filter['start_date']) && empty($filter['end_date'])) {
                                return '全部时间';
                            }
                            return '自定义: ' .
                                Carbon::parse($filter['start_date'])->format('Y-m-d') .
                                ' 至 ' .
                                Carbon::parse($filter['end_date'])->format('Y-m-d');
                        }

                        return match ($filter['preset']) {
                            'today' => '今日统计',
                            'yesterday' => '昨日统计',
                            'this_week' => '本周统计',
                            'this_month' => '本月统计',
                            'last_month' => '上月统计',
                            default => '全部时间'
                        };
                    })
                    ->alignRight()
                    ->sortable(false),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                // 时间范围筛选
                Tables\Filters\Filter::make('created_at')
                    ->label('统计时间')
                    ->form([
                        Forms\Components\Select::make('preset')
                            ->label('快速选择')
                            ->options([
                                'today' => '今日统计',
                                'yesterday' => '昨日统计',
                                'this_week' => '本周统计',
                                'this_month' => '本月统计',
                                'last_month' => '上月统计',
                                'custom' => '自定义时间段',
                            ])
                            ->live()
                            ->afterStateUpdated(function ($state, Set $set) {
                                if ($state === 'today') {
                                    $set('start_date', today());
                                    $set('end_date', today());
                                } elseif ($state === 'yesterday') {
                                    $set('start_date', today()->subDay());
                                    $set('end_date', today()->subDay());
                                } elseif ($state === 'this_week') {
                                    $set('start_date', today()->startOfWeek());
                                    $set('end_date', today()->endOfWeek());
                                } elseif ($state === 'this_month') {
                                    $set('start_date', today()->startOfMonth());
                                    $set('end_date', today()->endOfMonth());
                                } elseif ($state === 'last_month') {
                                    $set('start_date', today()->subMonth()->startOfMonth());
                                    $set('end_date', today()->subMonth()->endOfMonth());
                                }
                            }),
                        Forms\Components\DatePicker::make('start_date')
                            ->label('开始日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                        Forms\Components\DatePicker::make('end_date')
                            ->label('结束日期')
                            ->native(false)
                            ->displayFormat('Y-m-d')
                            ->hidden(fn (Get $get): bool => $get('preset') !== 'custom'),
                    ])
                    ->indicateUsing(function (array $data): ?string {
                        if ($data['preset'] === 'custom') {
                            if (!$data['start_date'] && !$data['end_date']) {
                                return null;
                            }

                            return '统计时间: ' .
                                Carbon::parse($data['start_date'])->format('Y-m-d') .
                                ' 至 ' .
                                Carbon::parse($data['end_date'])->format('Y-m-d');
                        }

                        return match ($data['preset']) {
                            'today' => '今日统计',
                            'yesterday' => '昨日统计',
                            'this_week' => '本周统计',
                            'this_month' => '本月统计',
                            'last_month' => '上月统计',
                            default => null
                        };
                    }),
            ], layout: Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(3);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAnchorCommissionStats::route('/'),
        ];
    }
}
