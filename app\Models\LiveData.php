<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class LiveData extends Model
{
    use HasFactory;

    protected $table = 'dymcn_livedata';

    protected $fillable = [
        'anchor_id',
        'anchor_account_id',
        'base_info',
        'start_time',
        'end_time',
        'viewers',
        'exposure',
        'clicks',
        'sales',
        'content_quality',
        'remark',
        'total_commission',
        'returned_commission',
        'real_commission',
        // 'payment_commission',
    ];



    /**
     * 获取拥有此 AnchorAccount 的主播 Anchor。
     */
    public function anchor(): BelongsTo
    {
        return $this->belongsTo(Anchor::class);
    }

    /**
     * 获取拥有此 AnchorAccount 的主播 Anchor。
     */
    public function anchorAccount(): BelongsTo
    {
        return $this->belongsTo(AnchorAccount::class);
    }



    /**
     * 本场直播下有多条佣金操作记录
     */
    public function liveDataCommissions(): HasMany
    {
        return $this->hasMany(LiveDataCommission::class, 'livedata_id', 'id');
    }


}
