<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    // return view('welcome');
    // return "本站点未对外开放!";
    // 重定向到/admin
    return redirect('/admin');
});

// 允许匿名访问
Route::get('/addons/dymcn/cookies', 'App\Http\Controllers\DymcnController@cookies');
Route::post('/addons/dymcn/cookies', 'App\Http\Controllers\DymcnController@cookies');



// http://dymcn.test/large_screen_api/config
// http://dymcn-laravel.ttmm.vip/large_screen_api/config
//允许跨域访问
Route::any('/large_screen_api/config', 'App\Http\Controllers\LargeScreenApiController@config')->middleware(\Illuminate\Http\Middleware\HandleCors::class);



// http://dymcn.test/large_screen_api/today_chart_data
// http://dymcn-laravel.ttmm.vip/large_screen_api/today_chart_data
//允许跨域访问
Route::any('/large_screen_api/today_chart_data', 'App\Http\Controllers\LargeScreenApiController@today_chart_data')->middleware(\Illuminate\Http\Middleware\HandleCors::class);



// http://dymcn.test/large_screen_api/month_chart_data
// http://dymcn-laravel.ttmm.vip/large_screen_api/month_chart_data
//允许跨域访问
Route::any('/large_screen_api/month_chart_data', 'App\Http\Controllers\LargeScreenApiController@month_chart_data')->middleware(\Illuminate\Http\Middleware\HandleCors::class);




// http://dymcn.test/large_screen_api/today_rank_list
// http://dymcn.test/large_screen_api/week_rank_list
// http://dymcn.test/large_screen_api/month_rank_list
// http://dymcn-laravel.ttmm.vip/large_screen_api/today_rank_list
// http://dymcn-laravel.ttmm.vip/large_screen_api/week_rank_list
// http://dymcn-laravel.ttmm.vip/large_screen_api/month_rank_list
//允许跨域访问
Route::any('/large_screen_api/today_rank_list', 'App\Http\Controllers\LargeScreenApiController@today_rank_list')->middleware(\Illuminate\Http\Middleware\HandleCors::class);
Route::any('/large_screen_api/week_rank_list', 'App\Http\Controllers\LargeScreenApiController@week_rank_list')->middleware(\Illuminate\Http\Middleware\HandleCors::class);
Route::any('/large_screen_api/month_rank_list', 'App\Http\Controllers\LargeScreenApiController@month_rank_list')->middleware(\Illuminate\Http\Middleware\HandleCors::class);


