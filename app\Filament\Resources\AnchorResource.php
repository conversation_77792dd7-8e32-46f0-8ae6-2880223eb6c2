<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AnchorResource\Pages;
use App\Filament\Resources\AnchorResource\RelationManagers;
use App\Models\Anchor;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Notifications\Notification;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Database\Eloquent\Model;

class AnchorResource extends Resource implements HasShieldPermissions
{
    protected static ?int $navigationSort = 10;

    protected static ?string $model = Anchor::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    public static ?string $label = '主播';

    protected static ?string $navigationLabel = '主播列表';

    protected static ?string $navigationGroup = 'MCN管理';

    public static function getEloquentQuery(): Builder
    {
        //如果是超级管理员,则返回所有
        if (auth()->user()->isSuperAdmin()) {
            return parent::getEloquentQuery();
        } else {
            if (static::can('view_any_user_data')) {
                return parent::getEloquentQuery();
            } else {
                return parent::getEloquentQuery()->where('user_id', auth()->user()->id);
            }
        }
    }


    public static function getPermissionPrefixes(): array
    {
        return array_merge(\BezhanSalleh\FilamentShield\Support\Utils::getGeneralResourcePermissionPrefixes(), [
            //显示任意用户数据功能
            'view_any_user_data',
            //是否可查看主播佣金(累计,已退,实际)
            'view_anchor_commission',
        ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('所属助理')
                            ->relationship('user', 'name')
                            ->required()
                            ->default(0),
                        Forms\Components\TextInput::make('real_name')
                            ->label('主播姓名')
                            ->required()
                            ->maxLength(255),
                        //avatar 头像上传
                        Forms\Components\FileUpload::make('avatar')
                            ->label('头像')
                            ->columnSpan(1)
                            ->directory('avatar')
                            ->image()
                            ->required(),

                        Forms\Components\FileUpload::make('avatar_big')
                            ->label('大头像')
                            ->columnSpan(1)
                            ->directory('avatar')
                            ->image()
                            ->required(),

                        Forms\Components\TextInput::make('phone')
                            ->label('电话号码')
                            ->tel()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('id_card')
                            ->label('身份证号码')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('total_commission')
                            ->label('累计佣金')
                            ->required()
                            ->numeric()
                            ->disabled()
                            ->default(0.00)
                            ->hidden(fn () => !(static::can('view_anchor_commission'))),
                        Forms\Components\TextInput::make('returned_commission')
                            ->label('累计已退佣')
                            ->required()
                            ->numeric()
                            ->disabled()
                            ->default(0.00)
                            ->hidden(fn () => !(static::can('view_anchor_commission'))),
                        Forms\Components\TextInput::make('real_commission')
                            ->label('实际佣金')
                            ->required()
                            ->numeric()
                            ->disabled()
                            ->default(0.00)
                            ->hidden(fn () => !(static::can('view_anchor_commission'))),
                        // Forms\Components\TextInput::make('payment_commission')
                        //     ->label('累计已结算')
                        //     ->required()
                        //     ->numeric()
                        //     ->disabled()
                        //     ->default(0.00),
                        Forms\Components\Textarea::make('remark')
                            ->label('备注信息')
                            ->rows(1)
                            ->columnSpan(1),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('所属助理')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\ImageColumn::make('avatar')
                    ->label('头像')
                    ->sortable(),
                Tables\Columns\TextColumn::make('real_name')
                    ->label('主播姓名')
                    ->searchable(),
                Tables\Columns\TextColumn::make('phone')
                    ->label('电话号码')
                    ->searchable(),
                Tables\Columns\TextColumn::make('id_card')
                    ->label('身份证号码')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('total_commission')
                    ->label('累计佣金')
                    ->numeric()
                    ->sortable()
                    ->hidden(fn () => !(static::can('view_anchor_commission'))),
                Tables\Columns\TextColumn::make('returned_commission')
                    ->label('累计已退佣')
                    ->numeric()
                    ->sortable()
                    ->hidden(fn () => !(static::can('view_anchor_commission'))),
                Tables\Columns\TextColumn::make('real_commission')
                    ->label('实际佣金')
                    ->numeric()
                    ->sortable()
                    ->hidden(fn () => !(static::can('view_anchor_commission'))),
                // Tables\Columns\TextColumn::make('payment_commission')
                //     ->label('累计已结算')
                //     ->numeric()
                //     ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                // 助理筛选
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('所属助理')
                    ->relationship('user', 'name')
                    ->searchable()
                    ->preload(),
            ], layout: Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(3)
            ->actions([
                Tables\Actions\ViewAction::make(), // 添加查看按钮
                Tables\Actions\EditAction::make(),

                Tables\Actions\ActionGroup::make([
                    Tables\Actions\DeleteAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('确认删除?')
                        ->modalSubheading("记得要保证本条数据下没有其他相关数据后再删除哦.")
                        ->before(function (Tables\Actions\DeleteAction $action, \App\Models\Anchor $record) {
                            if ($record->anchorAccounts()->count() > 0) {
                                Notification::make()
                                    ->danger()
                                    ->title('不能删除该账号')
                                    ->body('这个账号下存在有主播账号数据,不能删除!')
                                    ->send();
                                $action->halt();
                            }
                        })
                        ->after(function (Tables\Actions\DeleteAction $action, \App\Models\Anchor $record) {
                            $record->delete();
                        }),

                    \Rmsramos\Activitylog\Actions\ActivityLogTimelineTableAction::make('Activities')
                        ->label('变更日志')
                        ->hidden(fn () => !(static::can('view_anchor_commission'))),

                ])->label('更多操作')
                    ->size(\Filament\Support\Enums\ActionSize::Small)
                    ->color('primary'),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->searchPlaceholder('搜索主播姓名, 电话, 助理名')
            ->searchable();
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\AnchorAccountsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListAnchors::route('/'),
            'create' => Pages\CreateAnchor::route('/create'),
            'edit'   => Pages\EditAnchor::route('/{record}/edit'),
            'view'   => Pages\ViewAnchor::route('/{record}'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return [
            'real_name',
            'phone',
            'id_card',
            'remark',
        ];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            '主播姓名' => $record->real_name,
            '所属助理' => $record->user?->name ?? '未知',
            '电话' => $record->phone,
        ];
    }

}
