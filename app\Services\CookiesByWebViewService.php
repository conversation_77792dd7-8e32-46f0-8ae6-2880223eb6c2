<?php

namespace App\Services;

use App\Models\Cookies;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CookiesByWebViewService
{

    public static function getWebCodexxxxx($url, $useragent="", $cookies="", $timeout=30, $show_window=1, $min_show_time=0)
    {
        $electron_path = "/Users/<USER>/_CODE/PHP/dymcn-laravel/storage/daya/webview_mac_arm64/Electron.app/Contents/MacOS/Electron";
        $webview_app_path = "/Users/<USER>/_CODE/Web/DyMcnRunner/app.js";
        $shell_run_cmd = "{$electron_path} {$webview_app_path} --url='{$url}' --useragent='{$useragent}' --cookies='{$cookies}' --timeout={$timeout} --show-window={$show_window} --min-show-time={$min_show_time}";
        // 执行命令并捕获输出
        $return_var = shell_exec($shell_run_cmd);
        return $return_var;
    }
    public static function getWebCodeXX($url, $useragent="", $cookies="", $timeout=30, $show_window=1, $min_show_time=0)
    {
        $electron_path = "/Users/<USER>/_CODE/PHP/dymcn-laravel/storage/daya/webview_mac_arm64/Electron.app/Contents/MacOS/Electron";
        $webview_app_path = "/Users/<USER>/_CODE/Web/DyMcnRunner/app.js";
        $shell_run_cmd = "{$electron_path} {$webview_app_path} --url='{$url}' --useragent='{$useragent}' --cookies='{$cookies}' --timeout={$timeout} --show-window={$show_window} --min-show-time={$min_show_time}";
        // 执行命令并捕获输出
        $return_var = shell_exec($shell_run_cmd);
        return $return_var;
    }

    public static function getWebCode($url, $useragent="", $cookies="", $timeout=30, $show_window=1, $min_show_time=0)
    {
        $shell_run_cmd = "";
        if  (strpos(PHP_OS, 'Linux') !== false) {
            //ubuntu系统
            $webview_app_path = storage_path('daya/webview_ubuntu/dymcnrunner-0.0.6.AppImage');
            $shell_run_cmd = "xvfb-run {$webview_app_path} --no-sandbox --url='{$url}' --useragent='{$useragent}' --cookies='{$cookies}' --timeout={$timeout} --show-window={$show_window} --min-show-time={$min_show_time}";
        } else if(strpos(PHP_OS, 'Darwin') !== false){
            //mac系统
            $webview_app_path = storage_path('daya/webview_mac_arm64/dymcnrunner.app/Contents/MacOS/dymcnrunner');
            $shell_run_cmd = "{$webview_app_path} --url='{$url}' --useragent='{$useragent}' --cookies='{$cookies}' --timeout={$timeout} --show-window={$show_window} --min-show-time={$min_show_time}";
        }else{
            //Windows系统
            // $webview_app_path = storage_path('daya/webview_mac/dymcnrunner');
            // $shell_run_cmd = "{$webview_app_path} --url='{$url}' --useragent='{$useragent}' --cookies='{$cookies}' --timeout={$timeout} --show-window={$show_window} --min-show-time={$min_show_time}";
            throw new \Exception('不支持的操作系统');
        }
        //记录Log日志
        Log::write('info', "命令执行: ".$shell_run_cmd);

        // dump($shell_run_cmd);
        //执行此命令, 读取命令行输出, 返回给本函数
        return shell_exec($shell_run_cmd);
    }

    public static function checkCookies(Cookies $record)
    {
        // $web_code=self::getWebCode('https://www.browserscan.net/zh/', $record->useragent, "[]");
        $web_code=self::getWebCode('https://buyin.jinritemai.com/dashboard', $record->useragent, $record->cookies);

        //如果在源码中,找到了  <div class="workFlowbasicSettings__desc">设置</div> 则说明在线
        if (strpos($web_code, '<div class="workFlowbasicSettings__desc">设置</div>') !== false) {
            $result = true;
            $result_msg = "Cookies 有效";
        } else {
            $result = false;
            $result_msg = "Cookies 无效";
        }

        try {
            $record->update([
                'is_online' => $result ? 1 : -1,
                'remark'    => $result_msg,
                'keeptime'  => Carbon::now()->timestamp,
            ]);
        } catch (\Exception $e) {
            $result = false;
            $result_msg = $e->getMessage();
        }

        return $result;
    }

    //获取web_code基于某个uuid的
    public static function getWebCodeByUuid($uuid, $url)
    {
        $cookies_row = Cookies::where('uuid', $uuid)->where('is_online', 1)->orderBy('keeptime', 'asc')->first();
        //如果账号不存在,则报错
        if (!$cookies_row) {
            throw new \Exception('cookies不存在');
        }
        $web_code=self::getWebCode($url, $cookies_row->useragent, $cookies_row->cookies);
        @file_put_contents("web_code.html", $url."".PHP_EOL."".$web_code);
        return $web_code;
    }
}
