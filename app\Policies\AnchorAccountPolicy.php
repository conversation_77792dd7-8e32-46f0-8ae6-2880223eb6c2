<?php

namespace App\Policies;

use App\Models\User;
use App\Models\AnchorAccount;
use Illuminate\Auth\Access\HandlesAuthorization;

class AnchorAccountPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_anchor::account');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, AnchorAccount $anchorAccount): bool
    {
        return $user->can('view_anchor::account');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_anchor::account');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, AnchorAccount $anchorAccount): bool
    {
        return $user->can('update_anchor::account');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, AnchorAccount $anchorAccount): bool
    {
        return $user->can('delete_anchor::account');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_anchor::account');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, AnchorAccount $anchorAccount): bool
    {
        return $user->can('force_delete_anchor::account');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_anchor::account');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, AnchorAccount $anchorAccount): bool
    {
        return $user->can('restore_anchor::account');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_anchor::account');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, AnchorAccount $anchorAccount): bool
    {
        return $user->can('replicate_anchor::account');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_anchor::account');
    }


    //显示主播账号分值
    public function show_anchor_account_score(User $user): bool
    {
        return $user->can('show_anchor_account_score_anchor::account');
    }

    //刷新主播账号直播场次
    public function refresh_anchor_account_live_data(User $user): bool
    {
        return $user->can('refresh_anchor_account_live_data_anchor::account');
    }



    //是否可以查询任意用户的数据
    public function view_any_user_data(User $user): bool
    {
        return $user->can('view_any_user_data_anchor::account');
    }
}
