<?php

namespace App\Filament\Actions;

use App\Models\Cookies;

use App\Models\CookiesCommission;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;

// 别名命名空间
use Filament\Tables\Actions\Action as TableAction;
use Filament\Actions\Action as FilamentAction;
use Illuminate\Support\Facades\Auth;

class CookiesAction
{
    /**
     * 获取操作类实例
     *
     * @param string $actionType
     * @return \Filament\Tables\Actions\Action|\Filament\Actions\Action
     */
    private static function getActionInstance(string $actionType, string $action_name)
    {
        if ($actionType === 'Tables') {
            return TableAction::make($action_name);
        }

        return FilamentAction::make($action_name);
    }

    public static function clearInvalidCookies(string $actionType,string $reqType, string $actionLabel="", int $clearIsOnlineType = 0)
    {
        if (empty($actionLabel)) {
            $actionLabel = "清理Cookies:".Cookies::getIsOnlineTypeList()["{$clearIsOnlineType}"];
        }
        $action = self::getActionInstance($actionType, $reqType);
        return $action->label($actionLabel)
            ->action(function () use($reqType, $action, $clearIsOnlineType) {

                //是否在线:-1=不在线,0=未知,1=在线
                Cookies::where('is_online', $clearIsOnlineType)->delete();

                Notification::make()
                    ->title('清理完成')
                    ->body('已经清理掉所有所要求的Cookies!')
                    ->success()
                    ->send();
                // 使用 Livewire 的 redirect 方法刷新页面
                return redirect(request()->header('Referer'));
            });
    }

    public static function clearCookiesByDay(string $actionType,string $reqType, string $actionLabel="", int $day = 30)
    {
        if (empty($actionLabel)) {
            $actionLabel = "清理Cookies:"."{$day}天前";
        }
        $action = self::getActionInstance($actionType, $reqType);
        return $action->label($actionLabel)
            ->action(function () use($reqType, $action, $day) {

                //删除$day前的数据 created_at
                Cookies::where('created_at', '<', now()->subDays($day))->delete();

                Notification::make()
                    ->title('清理完成')
                    ->body('已经清理掉所有所要求的Cookies!')
                    ->success()
                    ->send();
                // 使用 Livewire 的 redirect 方法刷新页面
                return redirect(request()->header('Referer'));
            });
    }
}
