<?php

namespace App\Filament\Resources\AnchorResource\Pages;

use App\Filament\Resources\AnchorResource;
use Filament\Resources\Pages\ViewRecord;

class ViewAnchor extends ViewRecord
{
    protected static string $resource = AnchorResource::class;

    protected function getHeaderWidgets(): array
    {
        return [
            // Add widgets if needed
        ];
    }

    protected function getActions(): array
    {
        return [
            // Add actions if needed
        ];
    }

    protected function getRelations(): array
    {
        return [
            AnchorResource::getRelations(),
        ];
    }
}
