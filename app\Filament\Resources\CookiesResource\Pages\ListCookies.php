<?php

namespace App\Filament\Resources\CookiesResource\Pages;

use App\Filament\Resources\CookiesResource;
use BezhanSalleh\FilamentShield\Traits\HasPanelShield;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Resources\Pages\ListRecords;

class ListCookies extends ListRecords
{

    protected static string $resource = CookiesResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
            //新增自定义按钮,清理Cookies,需要判断是否支持 clear_online_cookies_cookies 权限
            //hidden(false)=>显示  hidden(true)=隐藏
            \App\Filament\Actions\CookiesAction::clearInvalidCookies("", "clearInvalidCookiesByOnline", "", 1)->hidden(fn()=>(
                !Filament::auth()->user()->can("clear_online_cookies_cookies")
            )),
            \App\Filament\Actions\CookiesAction::clearInvalidCookies("", "clearInvalidCookiesByUnknown", "", 0)->hidden(fn()=>(
                !Filament::auth()->user()->can("clear_offline_cookies_cookies")
            )),
            \App\Filament\Actions\CookiesAction::clearInvalidCookies("", "clearInvalidCookiesByOffline", "", -1)->hidden(fn()=>(
                !Filament::auth()->user()->can("clear_offline_cookies_cookies")
            )),
            \App\Filament\Actions\CookiesAction::clearCookiesByDay("", "clearCookiesByDay", "", 30)->hidden(fn()=>(
                !Filament::auth()->user()->can("clear_offline_cookies_cookies")
            )),
        ];
    }
}
