<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Cookies;
use Illuminate\Auth\Access\HandlesAuthorization;

class CookiesPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_cookies');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Cookies $cookies): bool
    {
        return $user->can('view_cookies');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_cookies');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Cookies $cookies): bool
    {
        return $user->can('update_cookies');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Cookies $cookies): bool
    {
        return $user->can('delete_cookies');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_cookies');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, Cookies $cookies): bool
    {
        return $user->can('force_delete_cookies');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_cookies');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, Cookies $cookies): bool
    {
        return $user->can('restore_cookies');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_cookies');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, Cookies $cookies): bool
    {
        return $user->can('replicate_cookies');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_cookies');
    }

    //是否可以查询任意用户的数据
    public function view_any_user_data(User $user): bool
    {
        return $user->can('view_any_user_data_cookies');
    }

    //清理不在线的cookies
    public function clear_offline_cookies(User $user): bool
    {
        return $user->can('clear_offline_cookies_cookies');
    }

    //清理在线的cookies
    public function clear_online_cookies(User $user): bool
    {
        return $user->can('clear_online_cookies_cookies');
    }



}
