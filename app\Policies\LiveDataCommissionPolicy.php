<?php

namespace App\Policies;

use App\Models\User;
use App\Models\LiveDataCommission;
use Illuminate\Auth\Access\HandlesAuthorization;

class LiveDataCommissionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_live::data::commission');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, LiveDataCommission $liveDataCommission): bool
    {
        return $user->can('view_live::data::commission');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_live::data::commission');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, LiveDataCommission $liveDataCommission): bool
    {
        return $user->can('update_live::data::commission');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, LiveDataCommission $liveDataCommission): bool
    {
        return $user->can('delete_live::data::commission');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_live::data::commission');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, LiveDataCommission $liveDataCommission): bool
    {
        return $user->can('{{ ForceDelete }}');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('{{ ForceDeleteAny }}');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, LiveDataCommission $liveDataCommission): bool
    {
        return $user->can('{{ Restore }}');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('{{ RestoreAny }}');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, LiveDataCommission $liveDataCommission): bool
    {
        return $user->can('{{ Replicate }}');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('{{ Reorder }}');
    }

    /**
     * 判断用户是否可以审批佣金和拒绝佣金
     */
    public function approve_live_data_commission(User $user): bool
    {
        return $user->can('approve_live_data_commission_live::data::commission');
    }

    //是否可以查询任意用户的数据
    public function view_any_user_data(User $user): bool
    {
        return $user->can('view_any_user_data_live::data::commission');
    }

    //是否可以查看佣金所属场次详情
    public function view_live_data_commission_detail(User $user): bool
    {
        return $user->can('view_live_data_commission_detail_live::data::commission');
    }
}
