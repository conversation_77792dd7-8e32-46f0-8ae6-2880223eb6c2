<?php

return [
    'shield_resource' => [
        'should_register_navigation' => true,
        'slug' => 'shield/roles',
        'navigation_sort' => -1,
        'navigation_badge' => true,
        'navigation_group' => true,
        'is_globally_searchable' => false,
        'show_model_path' => true,
        'is_scoped_to_tenant' => true,
        'cluster' => null,
    ],

    'auth_provider_model' => [
        'fqcn' => 'App\\Models\\User',
    ],

    //超级管理员的角色名称
    'super_admin' => [
        'enabled' => true,
        'name' => '超级管理员',
        'define_via_gate' => false,
        'intercept_gate' => 'before', // after
    ],

    //面板用户的角色名称
    'panel_user' => [
        'enabled' => false,
        'name' => '基础管理员',
    ],

    //在配置文件中，可以添加或修改默认的权限前缀
    'permission_prefixes' => [
        'resource' => [
            //详情
            'view',
            //列表
            'view_any',
            //创建
            'create',
            //更新
            'update',
            // //恢复
            // 'restore',
            // //批量恢复
            // 'restore_any',
            // //复制
            // 'replicate',
            // //重新排序
            // 'reorder',
            //删除
            'delete',
            //批量删除
            'delete_any',
            // //永久删除
            // 'force_delete',
            // //批量永久删除
            // 'force_delete_any',
        ],

        'page' => 'page',
        'widget' => 'widget',
    ],

    'entities' => [
        'pages' => true,
        'widgets' => true,
        'resources' => true,
        'custom_permissions' => false,
    ],

    'generator' => [
        'option' => 'policies_and_permissions',
        'policy_directory' => 'Policies',
        'policy_namespace' => 'Policies',
    ],

    'exclude' => [
        'enabled' => true,

        'pages' => [
            'Dashboard',
        ],

        'widgets' => [
            'AccountWidget', 'FilamentInfoWidget',
        ],

        'resources' => [],
    ],

    'discovery' => [
        'discover_all_resources' => false,
        'discover_all_widgets' => false,
        'discover_all_pages' => false,
    ],

    'register_role_policy' => [
        'enabled' => true,
    ],

];
