<?php

namespace App\Providers\Filament;

use App\Models\User;
use Filament\Facades\Filament;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\NavigationItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;


class AdminPanelProvider extends PanelProvider
{
    public function boot()
    {
        Filament::serving(function () {
            Filament::registerNavigationItems([
                NavigationItem::make('Horizon')
                    ->visible(function(){ return auth()->user() && auth()->user()->isSuperAdmin();})
                    ->url("/".config('horizon.path'), shouldOpenInNewTab: true)
                    ->icon('heroicon-o-presentation-chart-line')
                    ->activeIcon('heroicon-s-presentation-chart-line')
                    ->label('技术队列监控')
                    ->group('开发维护')
                    ->sort(3),

                NavigationItem::make('数据大屏')
                    ->url('https://ai.goviewlink.com/chart/preview/clz0nk8et012pi1xqg4ah528l', shouldOpenInNewTab: true)
                    ->icon('heroicon-o-presentation-chart-bar')
                    ->activeIcon('heroicon-s-presentation-chart-bar')
                    ->sort(1),
            ]);
        });
    }
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->maxContentWidth(\Filament\Support\Enums\MaxWidth::Full)
            ->navigationGroups([
                'MCN管理',
                '设置',
                '开发维护',
            ])
            ->databaseNotifications()
            ->databaseNotificationsPolling('5s') // 设置轮询时间为30秒
            ->login()
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                //账号信息
                Widgets\AccountWidget::class,
                //账号角色权限
                \App\Filament\Widgets\AccountRolesWidget::class,

                // 官方的版权信息
                // Widgets\FilamentInfoWidget::class,
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            //灯丝权限盾
            ->plugins([
                \Rmsramos\Activitylog\ActivitylogPlugin::make()
                    ->navigationGroup('开发维护')
                    ->authorize(
                        fn() => auth()->user()->hasPermissionTo('view_any_activitylog')
                    ),


                //健康检测
                \ShuvroRoy\FilamentSpatieLaravelHealth\FilamentSpatieLaravelHealthPlugin::make()
                    ->navigationGroup('开发维护')
                    ->navigationLabel('系统状态检测')
                    ->usingPage(\App\Filament\Pages\HealthCheckResults::class),
                //面板的个人中心
                \Jeffgreco13\FilamentBreezy\BreezyCore::make()->myProfile(
                    shouldRegisterUserMenu: true, // Sets the 'account' link in the panel User Menu (default = true)
                    shouldRegisterNavigation: false, // Adds a main navigation item for the My Profile page (default = false)
                    navigationGroup: 'Settings', // Sets the navigation group for the My Profile page (default = null)
                    hasAvatars: true, // Enables the avatar upload form component (default = false)
                    slug: 'my-profile' // Sets the slug for the profile page (default = 'my-profile')
                )->customMyProfilePage(\App\Filament\Pages\MyProfilePage::class)
                    ->avatarUploadComponent(fn($fileUpload) => $fileUpload->disableLabel())
                    // ->enableSanctumTokens(
                    //     permissions: ["create", "view", "update", "delete"] // optional, customize the permissions (default = ["create", "view", "update", "delete"])
                    // )
                ,
                //面板的权限盾
                \BezhanSalleh\FilamentShield\FilamentShieldPlugin::make()
            ])
            // //注册 z3d0x/filament-logger 活动日志资源
            // ->resources([
            //     config('filament-logger.activity_resource')
            // ])
            ;
    }
}
