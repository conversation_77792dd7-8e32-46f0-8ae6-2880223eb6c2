<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Hash;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    public static ?string $label = '管理员';

    protected static ?string $navigationLabel = '管理员列表';

    protected static ?string $navigationIcon = 'heroicon-o-user';

    protected static ?string $navigationGroup = '设置';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label("用户名")
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('email')
                    ->label("电子邮箱")
                    ->email()
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('password')
                    ->label("密码")
                    ->password()
                    ->dehydrateStateUsing(fn($state) => Hash::make($state))
                    ->dehydrated(fn($state) => filled($state))
                    ->required(fn(string $context): bool => $context === 'create')
                    ->maxLength(255),
                //权限角色盾
                Forms\Components\CheckboxList::make('roles')
                    ->label("权限角色盾")
                    ->relationship('roles', 'name')
                    ->searchable(),
                Forms\Components\Textarea::make('two_factor_secret')
                    ->label("双因素认证密钥")
                    ->maxLength(65535)
                    ->columnSpanFull(),
                Forms\Components\Textarea::make('two_factor_recovery_codes')
                    ->label("双因素认证恢复码")
                    ->maxLength(65535)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label("用户名")
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->label("电子邮箱")
                    ->searchable(),
                //权限角色盾,使用模型中的getRoleNames()方法
                Tables\Columns\TextColumn::make('roles')
                    ->label("权限角色盾")
                    //显示为label
                    ->formatStateUsing(fn(Model $record) => $record->getRoleNames()->implode(', ')),
                Tables\Columns\TextColumn::make('created_at')
                    ->label("创建时间")
                    ->dateTime("Y-m-d H:i:s")
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label("最后更新时间")
                    ->dateTime("Y-m-d H:i:s")
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Impersonate::make(), //扮演
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
