<?php

namespace App\Console\Commands;

use App\Jobs\CheckLiveDataJob;
use App\Models\AnchorAccount;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class ScheduleCheckLiveData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:schedule-check-live-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '调度运行检测直播场次数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //输出当前时间
        echo Carbon::now().PHP_EOL;
        echo "PHP调度被执行".PHP_EOL;
        echo "------------------------".PHP_EOL;

        // 获取主播账号,有归属到某个助理id的主播账号
        $anchorAccountList = AnchorAccount::where(function ($query) {
            $query->where('anchor_id', '>', 0);
        })
        ->whereHas('cookies', function($query) {
            $query->where('is_online', 1); // 只获取有在线cookies的账号
        })
        ->get();

        // 分批处理，每批10个账号
        $chunks = $anchorAccountList->chunk(10);
        foreach ($chunks as $chunk) {
            foreach ($chunk as $anchorAccount) {
                // 添加随机延迟，避免同时执行
                $delay = rand(0, 30);
                CheckLiveDataJob::dispatch($anchorAccount->account_uid)
                    ->delay(now()->addSeconds($delay));
            }
            // 每批之间暂停2秒，避免瞬间推送太多任务
            sleep(2);
        }
    }
}
