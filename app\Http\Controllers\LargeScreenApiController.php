<?php

namespace App\Http\Controllers;

use App\Models\Anchor;
use App\Models\Cookies;
use App\Models\LiveData;
use App\Settings\GeneralSettings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LargeScreenApiController extends Controller
{

    // 大屏基础接口
    public function test(Request $request)
    {
        // 获取 POST 请求中的json数据包
        $jsonData = $request->json()->all();

        $download = $request->input('download');

        return response()->json(
            [
                'code' => 1,
                'msg'  => '配置获取成功',
                'data' => ""
            ],
            200, [], JSON_UNESCAPED_UNICODE);
    }

    private static function formatNumber($number, $decimals = 3)
    {
        $units = ['', '万', '亿', '万亿'];
        $unitIndex = 0;

        // 迭代找到合适的单位
        while ($number >= 10000 && $unitIndex < count($units) - 1) {
            $number /= 10000;
            $unitIndex++;
        }

        // 格式化为最多3位数字和2位小数点
        $formattedNumber = number_format($number, $decimals, '.', '');

        // 移除小数点后的0（如果有）
        $formattedNumber = rtrim(rtrim($formattedNumber, '0'), '.');

        return [
            'number' => $formattedNumber,
            'unit'   => $units[$unitIndex]
        ];
    }

    //只移除小数点后面的0值
    private static function removeTrailingZeros($number)
    {
        // 检查数字是否包含小数点
        if (strpos($number, '.') !== false) {
            // 移除小数部分多余的0
            $number = rtrim($number, '0');

            // 如果小数部分已经完全移除，只剩下小数点，则移除小数点
            $number = rtrim($number, '.');
        }
        return $number;
    }

    // 时长秒转换为人性化的时长
    private static function secondToHuman($second)
    {
        //转换为N小时N分钟N秒
        $hours = floor($second / 3600);
        $minutes = floor(($second % 3600) / 60);
        $seconds = $second % 60;
        return "{$hours}时{$minutes}分";
        // return "{$hours}时{$minutes}分{$seconds}秒";
    }



    // 配置接口
    // http://dymcn.test/large_screen_api/config
    // http://dymcn-laravel.ttmm.vip/large_screen_api/config
    public function config(Request $request)
    {
        $setting = app(GeneralSettings::class);

        //展示次数
        $goods_view_base = $setting->goods_view_base * 10000;
        //销售额
        $goods_sale_base = $setting->goods_sale_base * 10000;

        // 需要加上直播场次的累计展示量
        $db_goods_view = LiveData::sum('exposure');

        // 需要加上直播场次表的累计销售额
        $db_goods_sale = LiveData::sum('sales');

        $goods_view_base_format = self::formatNumber($goods_view_base + $db_goods_view);
        $goods_sale_base_format = self::formatNumber($goods_sale_base + $db_goods_sale);


        //直播场次
        $live_number_base = $setting->live_number_base;
        // 需要加上直播场次表的直播场次
        $db_live_number = LiveData::count();
        $live_number_base_format = self::formatNumber($live_number_base + $db_live_number, 3);

        //直播时长
        $live_hour_base = $setting->live_hour_base;
        // 需要加上直播场次表的直播时长
        $db_live_duration_second = LiveData::sum('duration_second');
        // 将秒$db_live_duration_second转换为小时
        $db_live_hour = $db_live_duration_second / 3600;
        $live_hour_base_format = self::formatNumber($live_hour_base + $db_live_hour, 3);

        return response()->json(
            [
                'code' => 1,
                'msg'  => '配置获取成功',
                'data' => [
                    //大屏标题
                    'large_screen_title'      => $setting->large_screen_title,

                    //销售额
                    'goods_sale_base'         => $goods_sale_base,
                    //展示次数
                    'goods_view_base'         => $goods_view_base,
                    //销售额
                    'goods_sale_base_format'  => $goods_sale_base_format,
                    //展示次数
                    'goods_view_base_format'  => $goods_view_base_format,

                    //直播场次
                    'live_number_base'        => $live_number_base,
                    'live_number_base_format' => $live_number_base_format,
                    //直播时长
                    'live_hour_base'          => $live_hour_base,
                    'live_hour_base_format'   => $live_hour_base_format,

                    //员工总数
                    'all_number_people'       => $setting->all_number_people,
                    //主播总数
                    'all_number_anchor'       => $setting->all_number_anchor,

                    //最后配置下发时间
                    'response_time'           => date('Y-m-d H:i:s')
                ]
            ],
            200, [], JSON_UNESCAPED_UNICODE);
    }


    // 今日数据图表
    // http://dymcn.test/large_screen_api/today_chart_data
    // http://dymcn-laravel.ttmm.vip/large_screen_api/today_chart_data
    public function today_chart_data()
    {
        //将今日的24小时每隔3小时分割成8个点
        $today_hours_key = [
            'hours',
            'live_number',
            'live_hour',
            'goods_sale',
            'goods_view',
        ];
        $today_hours_data = [];
        $hour_interval = 3;
        for ($i = 0; $i < 24; $i += $hour_interval) {
            //获取时间段整点的开始时间和结束时间
            $start_time = \Carbon\Carbon::now()->startOfDay()->addHours($i)->format('Y-m-d H:i:s');
            $end_time = \Carbon\Carbon::now()->startOfDay()->addHours($i + $hour_interval)->format('Y-m-d H:i:s');

            $hours = $i . '-' . ($i + $hour_interval) . '点';

            //如果当前时间大于了当前时间，则跳过
            if ($start_time > \Carbon\Carbon::now()->format('Y-m-d H:i:s')) {
                $today_hours_item=[
                    'hours'       => $hours,
                ];
                $today_hours_data[] = $today_hours_item;
                continue;
            }


            // $live_number = rand(0, 100);//场次
            // $live_hour = rand(1, 20);//时长
            // $goods_sale = rand(10, 50);//销售额
            // $goods_view = rand(100, 5000);//展示次数

            //直播场次数
            $live_number = LiveData::whereBetween('end_time', [$start_time, $end_time])->count();
            //直播时长秒 - 转换为小时
            $live_second = LiveData::whereBetween('end_time', [$start_time, $end_time])->sum('duration_second');
            //转换为小时,并保留4位小数
            $live_hour = number_format($live_second / 3600, 2);

            //直播销售额 转换为 万
            $goods_sale = LiveData::whereBetween('end_time', [$start_time, $end_time])->sum('sales');
            $goods_sale = number_format($goods_sale / 10000, 3);
            //直播展示次 转换为 万
            $goods_view = LiveData::whereBetween('end_time', [$start_time, $end_time])->sum('exposure');
            $goods_view = number_format($goods_view / 10000, 3);

            $today_hours_item=[
                'hours'       => $hours,
                'live_number' => $live_number,
                'live_hour'   => $live_hour,
                'goods_sale'  => $goods_sale,
                'goods_view'  => $goods_view,
            ];

            $today_hours_data[] = $today_hours_item;
        }
        $chart_data = [
            'dimensions' => $today_hours_key,
            'source'     => $today_hours_data,
        ];
        //将今日数据中的live_number累计起来
        $live_number = array_sum(array_column($today_hours_data, 'live_number'));
        $live_hour = array_sum(array_column($today_hours_data, 'live_hour'));

        //销售额
        $goods_sale = array_sum(array_column($today_hours_data, 'goods_sale'));
        //展示次数
        $goods_view = array_sum(array_column($today_hours_data, 'goods_view'));

        //保留3位小数
        $goods_sale = number_format($goods_sale, 3);
        $goods_view = number_format($goods_view, 3);

        $goods_sale = self::removeTrailingZeros($goods_sale);
        $goods_view = self::removeTrailingZeros($goods_view);

        $month_day=date('m月d日');
        $title = "今日直播数据({$month_day})";

        return response()->json(
            [
                'code' => 1,
                'msg'  => $title,
                'data' => [
                    //标题
                    'title'         => $title,
                    //直播场次(次)
                    'live_number'   => $live_number,
                    //今日展示时长(小时)
                    'live_hour'     => $live_hour,
                    //今日销售额(万元)
                    'goods_sale'    => $goods_sale,
                    //今日展示次数(万次)
                    'goods_view'    => $goods_view,

                    //图表数据
                    'chart_data'    => $chart_data,

                    //最后配置下发时间
                    'response_time' => date('Y-m-d H:i:s')
                ]
            ],
            200, [], JSON_UNESCAPED_UNICODE);
    }




    // 月数据图表
    // http://dymcn.test/large_screen_api/month_chart_data
    // http://dymcn-laravel.ttmm.vip/large_screen_api/month_chart_data
    public function month_chart_data()
    {
        //将本月的每一天数据进行统计
        $month_hours_key = [
            'hours',
            'live_number',
            'live_hour',
            'goods_sale',
            'goods_view',
        ];
        //获取本月有多少天
        $daysInMonth = \Carbon\Carbon::now()->daysInMonth;

        $month_hours_data = [];
        for ($i = 0; $i < $daysInMonth; $i += 1) {
            //获取本月的第几天的开始时间和结束时间
            $start_time = \Carbon\Carbon::now()->startOfMonth()->addDay($i)->format('Y-m-d');
            $end_time = \Carbon\Carbon::now()->startOfMonth()->addDay($i)->endOfDay()->format('Y-m-d H:i:s');

            $hours = "" . ($i + 1) . '日';

            //如果当前日期大于了今日的日期，则不统计, 跳过
            if ($start_time > \Carbon\Carbon::now()->format('Y-m-d')) {
                $month_hours_item = [
                    'hours'       => $hours,
                ];
                $month_hours_data[] = $month_hours_item;
                continue;
            }


            // $live_number = rand(0, 100);//场次
            // $live_hour = rand(1, 20);//时长
            // $goods_sale = rand(10, 50);//销售额
            // $goods_view = rand(100, 5000);//展示次数

            //直播场次数
            $live_number = LiveData::whereBetween('end_time', [$start_time, $end_time])->count();
            //直播时长秒 - 转换为小时
            $live_second = LiveData::whereBetween('end_time', [$start_time, $end_time])->sum('duration_second');
            //转换为小时,并保留4位小数
            $live_hour = number_format($live_second / 3600, 2);

            //直播销售额 转换为 万
            $goods_sale = LiveData::whereBetween('end_time', [$start_time, $end_time])->sum('sales');
            $goods_sale = number_format($goods_sale / 10000, 3);
            //直播展示次 转换为 万
            $goods_view = LiveData::whereBetween('end_time', [$start_time, $end_time])->sum('exposure');
            $goods_view = number_format($goods_view / 10000, 3);

            $month_hours_item = [
                'hours'       => $hours,
                'live_number' => $live_number,
                'live_hour'   => $live_hour,
                'goods_sale'  => $goods_sale,
                'goods_view'  => $goods_view,
            ];

            $month_hours_data[] = $month_hours_item;
        }
        $chart_data = [
            'dimensions' => $month_hours_key,
            'source'     => $month_hours_data,
        ];
        //将今日数据中的live_number累计起来
        $live_number = array_sum(array_column($month_hours_data, 'live_number'));
        $live_hour = array_sum(array_column($month_hours_data, 'live_hour'));

        //销售额
        $goods_sale = array_sum(array_column($month_hours_data, 'goods_sale'));
        //展示次数
        $goods_view = array_sum(array_column($month_hours_data, 'goods_view'));

        //保留3位小数
        $goods_sale = number_format($goods_sale, 3);
        $goods_view = number_format($goods_view, 3);

        $goods_sale = self::removeTrailingZeros($goods_sale);
        $goods_view = self::removeTrailingZeros($goods_view);

        $month=date('m月');
        $title = "{$month}销售走势图";

        return response()->json(
            [
                'code' => 1,
                'msg'  => $title,
                'data' => [
                    //标题
                    'title'         => $title,
                    //直播场次(次)
                    'live_number'   => $live_number,
                    //今日展示时长(小时)
                    'live_hour'     => $live_hour,
                    //今日销售额(万元)
                    'goods_sale'    => $goods_sale,
                    //今日展示次数(万次)
                    'goods_view'    => $goods_view,

                    //图表数据
                    'chart_data'    => $chart_data,

                    //最后配置下发时间
                    'response_time' => date('Y-m-d H:i:s')
                ]
            ],
            200, [], JSON_UNESCAPED_UNICODE);
    }


    //排行榜
    //https://dymcn-laravel.ttmm.vip/large_screen_api/today_rank_list?keys=_,主播,销售额,场数,时长,展示次,转化率,GMV比,时长比,展示比
    private static function rank_list($title, $start_time, $end_time, $keys = '')
    {
        $return_keys = explode(",", $keys);
        //删除$return_keys中的空字符串项
        $return_keys = array_filter($return_keys);
        $table_keys = [
            "_",
            "主播",
            "销售额",
            "场数",
            "时长",
            "展示次",
            "转化率",
            "GMV比",
            "时长比",
            "展示比",
        ];
        if (count($return_keys) == 0) {
            //返回完整的字段
            $return_keys = $table_keys;
        } else {
            //取出交集,允许返回的字段
            $return_keys = array_intersect($return_keys, $table_keys);
        }

        $table_dict_data = [];

        $total_anchor_goods_sale = 0;//销售额
        $total_anchor_live_number = 0;//场次
        $total_anchor_live_second = 0;//时长秒
        $total_anchor_goods_view = 0;//展示次

        //读取所有的主播
        $all_anchors = Anchor::all();
        foreach ($all_anchors as $anchor) {
            //主播名称
            $anchor_real_name = $anchor->real_name;

            //统计主播下的所有账号的数据
            $anchor_goods_sale = 0;//销售额
            $anchor_live_number = 0;//场次
            $anchor_live_second = 0;//时长秒
            $anchor_goods_view = 0;//展示次

            //读取该主播的直播数据来统计
            $anchor_live_number = LiveData::where('anchor_id', $anchor->id)->whereBetween('end_time', [$start_time, $end_time])->count();
            $anchor_live_second = LiveData::where('anchor_id', $anchor->id)->whereBetween('end_time', [$start_time, $end_time])->sum('duration_second');
            $anchor_goods_sale = LiveData::where('anchor_id', $anchor->id)->whereBetween('end_time', [$start_time, $end_time])->sum('sales');
            $anchor_goods_view = LiveData::where('anchor_id', $anchor->id)->whereBetween('end_time', [$start_time, $end_time])->sum('exposure');

            //转化比率=销售额/展示次 保留2位小数,转换为百分数
            $anchor_goods_sale_percent = $anchor_goods_view <= 0 ? 0 : number_format($anchor_goods_sale / $anchor_goods_view, 2);
            $anchor_goods_sale_percent = $anchor_goods_sale_percent ? $anchor_goods_sale_percent : 0;
            $anchor_goods_sale_percent = self::removeTrailingZeros($anchor_goods_sale_percent * 100);
            $anchor_goods_sale_percent = $anchor_goods_sale_percent ? $anchor_goods_sale_percent : 0;
            $anchor_goods_sale_percent = $anchor_goods_sale_percent . '%';

            $total_anchor_goods_sale = $total_anchor_goods_sale + $anchor_goods_sale;
            $total_anchor_live_number = $total_anchor_live_number + $anchor_live_number;
            $total_anchor_live_second = $total_anchor_live_second + $anchor_live_second;
            $total_anchor_goods_view = $total_anchor_goods_view + $anchor_goods_view;


            $table_item = [
                '_'      => 0,
                '主播'   => $anchor_real_name,
                '销售额' => $anchor_goods_sale,
                '场数'   => $anchor_live_number,
                '时长'   => $anchor_live_second,
                '展示次' => $anchor_goods_view,
                '转化率' => $anchor_goods_sale_percent,
                'GMV比'  => '0.00%',
                '时长比' => '0.00%',
                '展示比' => '0.00%',
            ];
            array_push($table_dict_data, $table_item);
        }


        //将$table_dict_data中的每一项按$anchor_goods_sale进行排序
        usort($table_dict_data, function ($a, $b) {
            return $b['销售额'] - $a['销售额'];
        });
        //遍历$table_dict_data,给每一项的#赋值, 从1开始
        for ($i = 0; $i < count($table_dict_data); $i++) {
            $table_dict_data[$i]['_'] = $i + 1;
            //计算GMV占比
            $gmv_percent = $total_anchor_goods_sale <= 0 ? 0 : number_format($table_dict_data[$i]['销售额'] / $total_anchor_goods_sale, 2);
            $gmv_percent = $gmv_percent ? $gmv_percent : 0;
            $gmv_percent = self::removeTrailingZeros($gmv_percent * 100);
            $gmv_percent = $gmv_percent ? $gmv_percent : 0;
            $gmv_percent = $gmv_percent . '%';
            $table_dict_data[$i]['GMV比'] = $gmv_percent;

            //计算时长占比
            $duration_percent = $total_anchor_live_second <= 0 ? 0 : number_format($table_dict_data[$i]['时长'] / $total_anchor_live_second, 2);
            $duration_percent = $duration_percent ? $duration_percent : 0;
            $duration_percent = self::removeTrailingZeros($duration_percent * 100);
            $duration_percent = $duration_percent ? $duration_percent : 0;
            $duration_percent = $duration_percent . '%';
            $table_dict_data[$i]['时长比'] = $duration_percent;

            //计算展示占比
            $exposure_percent = $total_anchor_goods_view <= 0 ? 0 : number_format($table_dict_data[$i]['展示次'] / $total_anchor_goods_view, 2);
            $exposure_percent = $exposure_percent ? $exposure_percent : 0;
            $exposure_percent = self::removeTrailingZeros($exposure_percent * 100);
            $exposure_percent = $exposure_percent ? $exposure_percent : 0;
            $exposure_percent = $exposure_percent . '%';
            $table_dict_data[$i]['展示比'] = $exposure_percent;


            //将时长秒转换为人性化的时长
            $table_dict_data[$i]['时长'] = self::secondToHuman($table_dict_data[$i]['时长']);
        }


        //遍历$table_dict_data,并按$return_keys要求的key,返回有效的数据
        $table_data = [];
        for ($i = 0; $i < count($table_dict_data); $i++) {
            $table_item = [];
            for ($j = 0; $j < count($return_keys); $j++) {
                //当前的key键名称
                $key = $return_keys[$j];
                //如果值存在该键,则压入该值
                if (isset($table_dict_data[$i][$key])) {
                    array_push($table_item, $table_dict_data[$i][$key]);
                } else {
                    array_push($table_item, '');
                }
            }
            if (count($table_item) > 0) {
                array_push($table_data, $table_item);
            }
        }

        return response()->json(
            [
                'code' => 1,
                'msg'  => $title,
                'data' => [
                    //标题
                    'title'         => $title,
                    'start_time'    => $start_time,
                    'end_time'      => $end_time,
                    'table_count'   => count($table_dict_data),
                    'table_keys'    => implode(',', $return_keys),
                    'table_data'    => $table_data,
                    //最后配置下发时间
                    'response_time' => date('Y-m-d H:i:s')
                ]
            ],
            200, [], JSON_UNESCAPED_UNICODE);
    }

    //今日排行榜
    // http://dymcn.test/large_screen_api/today_rank_list
    // http://dymcn-laravel.ttmm.vip/large_screen_api/today_rank_list
    // http://dymcn-laravel.ttmm.vip/large_screen_api/today_rank_list?keys=_,主播,销售额,场数,时长,展示次,转化率,GMV比,时长比,展示比
    // http://dymcn-laravel.ttmm.vip/large_screen_api/today_rank_list?keys=_,主播,销售额,场数,时长,GMV比
    public function today_rank_list()
    {
        $keys = \request('keys', '');
        $title = "今日销售额排行榜";

        //今日的开始和结束时间
        $start_time = \Carbon\Carbon::now()->startOfDay()->format('Y-m-d H:i:s');
        $end_time = \Carbon\Carbon::now()->endOfDay()->format('Y-m-d H:i:s');

        return self::rank_list($title, $start_time, $end_time, $keys);
    }


    //本周排行榜
    // http://dymcn.test/large_screen_api/week_rank_list
    // http://dymcn-laravel.ttmm.vip/large_screen_api/week_rank_list
    // http://dymcn-laravel.ttmm.vip/large_screen_api/week_rank_list?keys=_,主播,销售额,场数,时长,展示次,转化率,GMV比,时长比,展示比
    // http://dymcn-laravel.ttmm.vip/large_screen_api/week_rank_list?keys=_,主播,销售额,场数,时长,GMV比
    public function week_rank_list()
    {
        $keys = \request('keys', '');
        $title = "本周销售额排行榜";

        $start_time = \Carbon\Carbon::now()->startOfWeek()->format('Y-m-d H:i:s');
        $end_time = \Carbon\Carbon::now()->endOfWeek()->format('Y-m-d H:i:s');

        return self::rank_list($title, $start_time, $end_time, $keys);
    }

    //本月排行榜
    // http://dymcn.test/large_screen_api/month_rank_list
    // http://dymcn-laravel.ttmm.vip/large_screen_api/month_rank_list
    // http://dymcn-laravel.ttmm.vip/large_screen_api/month_rank_list?keys=_,主播,销售额,场数,时长,展示次,转化率,GMV比,时长比,展示比
    // http://dymcn-laravel.ttmm.vip/large_screen_api/month_rank_list?keys=_,主播,销售额,场数,时长,GMV比
    public function month_rank_list()
    {
        $keys = \request('keys', '');
        $title = "本月销售额排行榜";

        $start_time = \Carbon\Carbon::now()->startOfMonth()->format('Y-m-d H:i:s');
        $end_time = \Carbon\Carbon::now()->endOfMonth()->format('Y-m-d H:i:s');

        return self::rank_list($title, $start_time, $end_time, $keys);
    }

}
