<x-filament-panels::page>
    <x-filament::tabs>
        <x-filament::tabs.item
            :active="$activeTab === 'general'"
            wire:click="$set('activeTab', 'general')"
            icon="heroicon-o-users"
            :badge="false">
            通用说明
        </x-filament::tabs.item>

        <x-filament::tabs.item
            :active="$activeTab === 'super-admin'"
            wire:click="$set('activeTab', 'super-admin')"
            icon="heroicon-o-key"
            :badge="false">
            超级管理员
        </x-filament::tabs.item>

        <x-filament::tabs.item
            :active="$activeTab === 'assistant'"
            wire:click="$set('activeTab', 'assistant')"
            icon="heroicon-o-user-group"
            :badge="false">
            助理用户组
        </x-filament::tabs.item>

        <x-filament::tabs.item
            :active="$activeTab === 'business'"
            wire:click="$set('activeTab', 'business')"
            icon="heroicon-o-briefcase"
            :badge="false">
            商务用户组
        </x-filament::tabs.item>

        <x-filament::tabs.item
            :active="$activeTab === 'finance'"
            wire:click="$set('activeTab', 'finance')"
            icon="heroicon-o-calculator"
            :badge="false">
            财务用户组
        </x-filament::tabs.item>

        <x-filament::tabs.item
            :active="$activeTab === 'system'"
            wire:click="$set('activeTab', 'system')"
            icon="heroicon-o-cog"
            :badge="false">
            系统维护
        </x-filament::tabs.item>
    </x-filament::tabs>

    <div class="mt-4 prose max-w-none dark:prose-invert">
        @if ($activeTab === 'general')
            {!! $generalHelp !!}
        @elseif ($activeTab === 'super-admin')
            {!! $superAdminHelp !!}
        @elseif ($activeTab === 'assistant')
            {!! $assistantHelp !!}
        @elseif ($activeTab === 'business')
            {!! $businessHelp !!}
        @elseif ($activeTab === 'finance')
            {!! $financeHelp !!}
        @elseif ($activeTab === 'system')
            {!! $systemHelp !!}
        @endif
    </div>
</x-filament-panels::page>
