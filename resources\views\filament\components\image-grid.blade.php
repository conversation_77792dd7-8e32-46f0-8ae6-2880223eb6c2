@php
    $state = $getState() ?? [];

    foreach ($state as &$imageUrl) {
        $imageUrl = asset('storage/' . $imageUrl);
    }
@endphp

@if (count($state) > 0)
    <div class="flex flex-wrap gap-2">
        @foreach ($state as $imageUrl)
            <div class="relative group">
                <img
                    src="{{ $imageUrl }}"
                    alt="申请相关图片"
                    class="w-20 h-20 object-cover rounded-md cursor-pointer hover:opacity-75 transition-opacity border border-gray-200"
                    onclick="window.open('{{ $imageUrl }}', '_blank')"
                    title="点击查看大图"
                />
            </div>
        @endforeach
    </div>
@endif
