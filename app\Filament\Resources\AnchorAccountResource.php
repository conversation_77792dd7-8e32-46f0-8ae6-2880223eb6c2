<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AnchorAccountResource\Pages;
use App\Filament\Resources\AnchorAccountResource\RelationManagers;
use App\Models\AnchorAccount;
use App\Services\BuyinService;
use <PERSON>zhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

use Illuminate\Support\Carbon;

use Illuminate\Support\Facades\Gate;

use function PHPUnit\Framework\isNull;

class AnchorAccountResource extends Resource implements HasShieldPermissions
{

    protected static ?int $navigationSort = 20;

    protected static ?string $model = AnchorAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-identification';

    public static ?string $label = '主播账号';

    protected static ?string $navigationLabel = '主播账号列表';

    protected static ?string $navigationGroup = 'MCN管理';

    // 添加全局搜索记录标题属性
    protected static ?string $recordTitleAttribute = 'account_name';

    // 配置全局搜索功能
    public static function getGloballySearchableAttributes(): array
    {
        return [
            'account_name',
            'real_name',
            'phone',
            'account_uid',
            'device',
            'anchor.real_name',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        //如果是超级管理员,则返回所有
        if (auth()->user()->isSuperAdmin()) {
            return parent::getEloquentQuery();
        }else if (static::can('view_any_user_data')) {
            return parent::getEloquentQuery();
        }else{
            //获取当前用户的主播id
            $anchor_ids=auth()->user()->anchors()->pluck('id')->toArray();
            //将主播id未归属是0,也加入到主播id中
            array_push($anchor_ids, 0);
            //限定查询出属于这些主播id的账号
            return parent::getEloquentQuery()->WhereIn('anchor_id', $anchor_ids);
        }
    }

    public static function getPermissionPrefixes(): array
    {
        return array_merge(\BezhanSalleh\FilamentShield\Support\Utils::getGeneralResourcePermissionPrefixes(), [
            //显示任意用户数据功能
            'view_any_user_data',
            //显示主播账号分值
            'show_anchor_account_score',
            //刷新主播账号直播场次
            'refresh_anchor_account_live_data',
        ]);
    }

    public static function form(Form $form): Form
    {

        return $form
            ->schema([
                Forms\Components\Grid::make(4)
                    ->schema([
                    Forms\Components\Select::make('anchor_id')
                        ->label('所属主播')
                        ->relationship('anchor', 'real_name')
                        ->required()
                        ->default(0),
                    Forms\Components\TextInput::make('account_uid')
                        ->label('百应ID')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('account_name')
                        ->label('账号名')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('account_status')
                        ->label('账号状态')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('real_name')
                        ->label('真实姓名')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('phone')
                        ->label('电话号码')
                        ->maxLength(255),
                    Forms\Components\TextInput::make('id_card_no')
                        ->label('身份证号码')
                        ->maxLength(255),
                    Forms\Components\TextInput::make('device')
                        ->label('账号设备')
                        ->maxLength(255),
                    Forms\Components\TextInput::make('certificate_no')
                        ->label('执照编号')
                        ->maxLength(255),
                    Forms\Components\TextInput::make('certificate_legal_person')
                        ->label('执照法人')
                        ->maxLength(255),
                    Forms\Components\FileUpload::make('certificate_image')
                        ->label('执照图')
                        ->directory('certificate')
                        ->columnSpan(2)
                        ->image(),
                    Forms\Components\TextInput::make('margin_amount')
                        ->label('保证金')
                        ->required()
                        ->numeric()
                        ->default(0.00),
                    Forms\Components\TextInput::make('reputation_score')
                        ->label('口碑分')
                        ->required()
                        ->numeric()
                        ->default(0.00),
                    Forms\Components\TextInput::make('credit_score')
                        ->label('信用分')
                        ->required()
                        ->numeric()
                        ->default(0.00),
                    Forms\Components\TextInput::make('price_score')
                        ->label('价格分')
                        ->required()
                        ->numeric()
                        ->default(0.00),
                    Forms\Components\Textarea::make('remark')
                        ->label('备注信息')
                        ->columnSpanFull(),
                ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //新增一个自定义字段
                Tables\Columns\TextColumn::make('about_account_name')
                    ->label('账号名称')
                    ->getStateUsing(function (AnchorAccount $record) {
                        $html = "";

                        if (is_null($record->anchor)) {
                            $html .= '<div><b>助理: ' . '<b class="text-danger-600">未知</b>' . '</b></div>';
                            $html .= '<div>主播: ' . '<b class="text-danger-600">未知</b>' . '</div>';
                            $html .= '<div>账号状态: ' . $record->account_status . '</div>';
                            $html .= '<div>账号名称: ' . $record->account_name . '</div>';
                        } else {
                            // 所属助理
                            if(!is_null($record->anchor->user)){
                                $html .= '<div><b>助理: ' . $record->anchor->user->name . '</b></div>';
                            }else{
                                $html .= '<div><b>助理: ' . '<b class="text-danger-600">未知</b>' . '</b></div>';
                            }
                            $html .= '<div>主播: ' . $record->anchor->real_name . '</div>';
                            $html .= '<div>账号状态: ' . $record->account_status . '</div>';
                            $html .= '<div>账号名称: ' . $record->account_name . '</div>';
                        }
                        return $html;
                    })
                    ->html()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query
                            ->where('account_name', 'like', "%{$search}%")
                            ->orWhere('account_status', 'like', "%{$search}%")
                            ->orWhereHas('anchor', function (Builder $query) use ($search) {
                                $query->where('real_name', 'like', "%{$search}%");
                            });
                    }),

                //新增一个自定义字段
                Tables\Columns\TextColumn::make('about_account_info')
                    ->label('账号信息')
                    ->getStateUsing(function (AnchorAccount $record) {
                        $html = "";
                        $html .= '<div>设备: ' . $record->device . '</div>';
                        $html .= '<div>真实名称: ' . $record->real_name . '</div>';
                        $html .= '<div>手机号码: ' . $record->phone . '</div>';
                        return $html;
                    })
                    ->html()
                    ->searchable(query: function (Builder $query, string $search): Builder {
                        return $query
                            ->where('device', 'like', "%{$search}%")
                            ->orWhere('real_name', 'like', "%{$search}%")
                            ->orWhere('phone', 'like', "%{$search}%");
                    }),

                //新增一个自定义字段
                Tables\Columns\TextColumn::make('about_account_score')
                    ->label('账号分数')
                    ->getStateUsing(function (AnchorAccount $record) {
                        $html = "";
                        $html .= '<div>口碑分: ' . $record->reputation_score . '</div>';
                        $html .= '<div>信用分: ' . $record->credit_score . '</div>';
                        $html .= '<div>价格分: ' . $record->price_score . '</div>';
                        return $html;
                    })
                    ->hidden(
                    //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                        !(self::can('show_anchor_account_score'))
                    )
                    ->html(),


                Tables\Columns\TextColumn::make('anchor.real_name')
                    ->label('所属主播')
                    ->numeric()
                    ->hidden()
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('account_uid')
                    ->label('百应ID')
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->searchable(),
                Tables\Columns\TextColumn::make('account_name')
                    ->label('账号名')
                    ->hidden()
                    ->searchable(),
                Tables\Columns\TextColumn::make('account_status')
                    ->label('账号状态')
                    ->hidden()
                    ->searchable(),
                Tables\Columns\TextColumn::make('real_name')
                    ->label('真实姓名')
                    ->hidden()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('phone')
                    ->label('电话号码')
                    ->hidden()
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('id_card_no')
                    ->label('身份证号码')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('device')
                    ->label('账号设备')
                    ->hidden()
                    ->searchable(),
                Tables\Columns\TextColumn::make('certificate_no')
                    ->label('执照编号')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('certificate_legal_person')
                    ->label('执照法人')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\ImageColumn::make('certificate_image')
                    ->label('执照图')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('margin_amount')
                    ->label('保证金')
                    ->numeric()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('reputation_score')
                    ->label('口碑分')
                    ->hidden()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('credit_score')
                    ->label('信用分')
                    ->hidden()
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price_score')
                    ->label('价格分')
                    ->hidden()
                    ->numeric()
                    ->sortable(),

                //当前账号关联的cookies中是否含有有效的数据
                Tables\Columns\TextColumn::make('cookies_status')
                    ->label('Cookies状态')
                    ->getStateUsing(function (AnchorAccount $record) {
                        $cookies = $record->cookies_latest_online_first();
                        $keeptime=$cookies->keeptime??0;
                        //laravel人性化的显示是多久之前的时间
                        $keeptime=Carbon::parse($keeptime)->diffForHumans();
                        if ($cookies) {
                            return "在线<br/>" . $keeptime;
                        } else {
                            return "已经掉线";
                        }
                    })
                    //支持html渲染
                        ->html()
                    ->color(function (string $state) {
                        $keyword_type_list = [
                            '已经掉线' => 'warning',
                            '在线'     => 'success',
                        ];
                        //如果找到查找到$state中包含该key,则返回该值
                        foreach ($keyword_type_list as $key => $value) {
                            if (strpos($state, $key) !== false) {
                                return $value;
                            }
                        }
                        return 'gray';
                    })
                    ->icon(function (string $state) {
                        $keyword_type_list = [
                            '已经掉线' => 'heroicon-o-x-circle',
                            '在线'     => 'heroicon-o-check-circle',
                        ];
                        //如果找到查找到$state中包含该key,则返回该值
                        foreach ($keyword_type_list as $key => $value) {
                            if (strpos($state, $key) !== false) {
                                return $value;
                            }
                        }
                        return 'gray';
                    }),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                // 助理筛选
                Tables\Filters\Filter::make('assistant')
                    ->label('所属助理')
                    ->form([
                        Forms\Components\Select::make('user_id')
                            ->label('选择助理')
                            ->searchable()
                            ->preload()
                            ->options(function () {
                                return \App\Models\User::whereHas('anchors', function ($query) {
                                    $query->whereHas('anchorAccounts');
                                })->pluck('name', 'id');
                            })
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['user_id'],
                            fn (Builder $query, $userId): Builder => $query->whereHas(
                                'anchor',
                                fn (Builder $q) => $q->where('user_id', $userId)
                            )
                        );
                    }),

                // 主播筛选
                Tables\Filters\SelectFilter::make('anchor_id')
                    ->label('所属主播')
                    ->relationship('anchor', 'real_name')
                    ->searchable()
                    ->preload(),


                // 账号状态筛选
                Tables\Filters\SelectFilter::make('account_status')
                    ->label('账号状态')
                    ->options(function () {
                        return \App\Models\AnchorAccount::distinct('account_status')->pluck('account_status', 'account_status')->toArray();
                    }),

            ], layout: Tables\Enums\FiltersLayout::AboveContent)
            ->filtersFormColumns(3)
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),

                Tables\Actions\ActionGroup::make([
                    Tables\Actions\DeleteAction::make()
                        ->requiresConfirmation()
                        ->modalHeading('确认删除?')
                        ->modalSubheading("确认删除此主播账号？此操作将同时删除与该账号相关的所有数据，包括直播场次、佣金记录、Cookies等。")
                        ->action(function (Tables\Actions\DeleteAction $action, \App\Models\AnchorAccount $record) {
                            // 开始事务
                            \Illuminate\Support\Facades\DB::beginTransaction();

                            try {
                                // 1. 删除相关的佣金日志
                                \App\Models\AnchorCommissionLog::where('anchor_account_id', $record->id)->delete();

                                // 2. 获取所有直播场次ID
                                $liveDataIds = $record->liveDatas()->pluck('id')->toArray();

                                // 3. 删除这些直播场次关联的佣金记录
                                if (!empty($liveDataIds)) {
                                    \App\Models\LiveDataCommission::whereIn('livedata_id', $liveDataIds)->delete();
                                }

                                // 4. 删除直播场次
                                $record->liveDatas()->delete();

                                // 5. 删除Cookies
                                $record->cookies()->delete();

                                // 6. 最后删除主播账号本身
                                $record->delete();

                                // 提交事务
                                \Illuminate\Support\Facades\DB::commit();

                                Notification::make()
                                    ->success()
                                    ->title('删除成功')
                                    ->body('已成功删除主播账号及其相关数据')
                                    ->send();
                            } catch (\Exception $e) {
                                // 回滚事务
                                \Illuminate\Support\Facades\DB::rollBack();

                                Notification::make()
                                    ->danger()
                                    ->title('删除失败')
                                    ->body("删除过程中发生错误: {$e->getMessage()}")
                                    ->send();

                                $action->halt();
                            }
                        }),

                    Tables\Actions\Action::make('refreshAccount')
                        ->label('刷新账号分值数据')
                        ->hidden(
                        //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                            !(self::can('refresh_anchor_account_live_data'))
                        )
                        ->action(function (AnchorAccount $record) {
                            try {
                                BuyinService::refreshAccount($record->account_uid);
                                Notification::make()
                                    ->title('操作成功')
                                    ->body("刷新账号的分值数据成功")
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('操作失败')
                                    ->body("异常:{$e->getMessage()}")
                                    ->danger()
                                    ->send();
                            }
                        }),
                    Tables\Actions\Action::make('get_livedata')
                        ->label('刷新直播场次数据')
                        ->hidden(
                        //当前用户是否有此权限, 如果有, 则不隐藏(取反)
                            !(self::can('refresh_anchor_account_live_data'))
                        )
                        ->action(function (AnchorAccount $record) {
                            try {
                                $livedata_count=BuyinService::refreshLivedata($record->account_uid);
                                Notification::make()
                                    ->title('操作成功')
                                    ->body("本次刷新了{$livedata_count}条(未去重)数据")
                                    ->success()
                                    ->send();
                            } catch (\Exception $e) {
                                Notification::make()
                                    ->title('操作失败')
                                    ->body("异常:{$e->getMessage()}")
                                    ->danger()
                                    ->send();
                            }
                        }),
                ])->label('更多操作')
                    ->size(\Filament\Support\Enums\ActionSize::Small)
                    ->color('primary'),
            ])
            ->bulkActions([
                // Tables\Actions\BulkActionGroup::make([
                //     Tables\Actions\DeleteBulkAction::make(),
                // ]),
            ])
            ->searchPlaceholder('搜索名称, 助理名, 主播名')
            ->searchable(); // 启用表格整体搜索功能
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\LiveDatasRelationManager::class,
            RelationManagers\CookiesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListAnchorAccounts::route('/'),
            'create' => Pages\CreateAnchorAccount::route('/create'),
            'edit'   => Pages\EditAnchorAccount::route('/{record}/edit'),
            'view'   => Pages\ViewAnchorAccount::route('/{record}'),
        ];
    }
}
