<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CookiesResource\Pages;
use App\Filament\Resources\CookiesResource\RelationManagers;
use App\Models\Cookies;
use App\Services\CookiesService;
use App\Tables\Columns\IconStatusColumn;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CookiesResource extends Resource implements HasShieldPermissions
{

    protected static ?int $navigationSort = 30;

    protected static ?string $model = Cookies::class;


    public static ?string $label = 'Cookies';

    protected static ?string $navigationLabel = 'Cookies列表';

    protected static ?string $navigationGroup = 'MCN管理';

    protected static ?string $navigationIcon = 'heroicon-o-key';
    public static function getEloquentQuery(): Builder
    {
        //如果是超级管理员,则返回所有
        if (auth()->user()->isSuperAdmin()) {
            return parent::getEloquentQuery();
        }else if (static::can('view_any_user_data')) {
            return parent::getEloquentQuery();
        }else{
            //获取当前用户的数据
            $cookies_ids=auth()->user()->cookies()->pluck('dymcn_cookies.id')->toArray();
            return parent::getEloquentQuery()->whereIn('id', $cookies_ids);
        }
    }


    public static function getPermissionPrefixes(): array
    {
        return array_merge(\BezhanSalleh\FilamentShield\Support\Utils::getGeneralResourcePermissionPrefixes(), [
            //显示任意用户数据功能
            'view_any_user_data',
            //清理不在线的cookies
            'clear_offline_cookies',
            //清理在线的cookies
            'clear_online_cookies',
        ]);
    }


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('uuid')
                    ->label('唯一标识')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('domain')
                    ->label('域名')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('useragent')
                    ->label('UserAgent')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Toggle::make('is_online')
                    ->label('在线状态')
                    ->default(0),
                //时间戳 keeptime 隐藏,新增时默认为当前时间
                Forms\Components\TextInput::make('keeptime')
                    ->label('保持时间')
                    ->default(time()),
                Forms\Components\Textarea::make('remark')
                    ->label('备注')
                    ->nullable()
                    ->maxLength(65535),
                Forms\Components\Textarea::make('cookies')
                    ->label('Cookies')
                    ->required()
                    ->rows(15)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('uuid')
                    ->label('唯一标识')
                    ->searchable(),
                Tables\Columns\TextColumn::make('domain')
                    ->label('域名')
                    ->searchable(),
                // Tables\Columns\TextColumn::make('useragent')
                //     ->label('UserAgent')
                //     ->searchable()
                //     ->sortable(),
                // Tables\Columns\TextColumn::make('cookies')
                //     ->label('Cookies')
                //     ->searchable()
                //     ->sortable(),
                // IconStatusColumn::make('is_online')
                //     ->label('在线状态'),

                Tables\Columns\IconColumn::make("is_online")
                    //是否在线:-1=不在线,0=未知,1=在线
                    ->label('状态')
                    ->color(fn (string $state): string => match ($state) {
                        '0' => 'gray',
                        '-1' => 'warning',
                        '1' => 'success',
                        default => 'warning',
                    })
                    ->icon(fn (string $state): string => match ($state) {
                        '0' => 'heroicon-o-exclamation-circle',
                        '-1' => 'heroicon-o-x-circle',
                        '1' => 'heroicon-o-check-circle',
                        default => 'heroicon-o-question-mark-circle',
                    })
                    ->tooltip(fn (string $state): string => match ($state) {
                        '0' => '未知',
                        '-1' => '不在线',
                        '1' => '在线',
                        default => '未知',
                    })
                    //居中
                ->alignCenter(),

                //时间戳 keeptime 显示未格式化的时间
                Tables\Columns\TextColumn::make('keeptime')
                    ->label('状态时间')
                    ->searchable()
                    ->dateTime("Y-m-d H:i:s")
                    ->sortable(),
                Tables\Columns\TextColumn::make('remark')
                    ->label('备注')
                    ->searchable()
                    ->sortable(),
            ])
            ->defaultSort('id', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('checkCookies')
                    ->label('检查 Cookies')
                    ->action(function (Cookies $record){
                        $result = CookiesService::checkCookies($record);
                        // 检查 cookies 是否有效
                        if ($result) {
                            Notification::make()
                                ->title('Cookies 有效')
                                ->body('这个 cookies 是有效的。')
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Cookies 无效')
                                ->body('这个 cookies 是无效的。')
                                ->danger()
                                ->send();
                        }
                    }),
                    // ->action(fn (Cookies $record) => WebdriverCookiesService::checkCookies($record)),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc'); // 这里设置按id倒序排序
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCookies::route('/'),
            'create' => Pages\CreateCookies::route('/create'),
            'edit' => Pages\EditCookies::route('/{record}/edit'),
        ];
    }
}
