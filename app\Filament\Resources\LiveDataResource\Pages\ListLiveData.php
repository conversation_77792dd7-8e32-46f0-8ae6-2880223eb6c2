<?php

namespace App\Filament\Resources\LiveDataResource\Pages;

use App\Filament\Resources\LiveDataResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLiveData extends ListRecords
{
    protected static string $resource = LiveDataResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
