<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *  主播账号表: 所属主播, 所属助理,
     * 百应ID,账号名,账号状态,
     * 真实姓名,身份证,账号设备,电话号码,
     * 执照编号,执照法人,营业执照图,
     * 保证金,口碑分,信用分,备注信息
     */
    public function up(): void
    {
        Schema::create('dymcn_anchor_account', function (Blueprint $table) {
            $table->id();
            $table->integer('anchor_id')->default(0)->comment('所属主播ID');

            $table->string('account_uid')->default('')->comment('账号UID');
            $table->string('account_name')->default('')->comment('账号名');
            $table->string('account_status')->default('')->comment('账号状态');

            $table->string('real_name')->default('')->comment('真实姓名');
            $table->string('phone')->default('')->comment('电话号码');
            $table->string('id_card_no')->default('')->comment('身份证号码');
            $table->string('device')->default('')->comment('账号设备');

            $table->string('certificate_no')->default('')->comment('执照编号');
            $table->string('certificate_legal_person')->default('')->comment('执照法人');
            $table->string('certificate_image')->default('')->comment('执照图');

            $table->decimal('margin_amount', 10, 2)->default(0.00)->comment('保证金');
            $table->decimal('reputation_score', 10, 2)->default(0.00)->comment('口碑分');
            $table->decimal('credit_score', 10, 2)->default(0.00)->comment('信用分');
            $table->decimal('price_score', 10, 2)->default(0.00)->comment('价格分');

            $table->text('remark')->nullable()->comment('备注信息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('anchor_account');
    }
};
