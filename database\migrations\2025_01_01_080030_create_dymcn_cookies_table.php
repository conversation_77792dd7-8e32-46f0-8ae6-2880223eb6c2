<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
    {
        Schema::create('dymcn_cookies', function (Blueprint $table) {
            $table->id();
            $table->string('uuid')->default('')->comment('唯一标识');
            $table->string('taburl')->default('')->comment('页面URL');
            $table->string('domain')->default('')->comment('域名');
            $table->string('useragent')->default('')->comment('UA');
            $table->longText('cookies')->comment('cookies');
            $table->longText('local_storage')->nullable()->comment('local_storage');
            $table->tinyInteger('is_online')->default(0)->comment('是否在线:-1=不在线,0=未知,1=在线');
            $table->bigInteger('keeptime')->comment('保活时间');
            $table->text('remark')->nullable()->comment('备注信息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cookies');
    }
};
