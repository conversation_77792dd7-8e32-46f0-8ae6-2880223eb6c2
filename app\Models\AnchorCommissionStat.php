<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

class AnchorCommissionStat extends Model
{
    use LogsActivity;
    use HasFactory;

    protected $table = 'dymcn_anchor';

    protected $fillable = [
        'real_name',
        'avatar',
        'avatar_big',
        'phone',
        'id_card',
        'total_commission',
        'returned_commission',
        // 'payment_commission',
        'remark',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly($this->fillable);
    }

    /**
     * 此主播拥有的账号
     */
    public function anchorAccounts(): HasMany
    {
        return $this->hasMany(AnchorAccount::class);
    }


    /**
     * 此主播所属的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function commissionLogs(): HasMany
    {
        return $this->hasMany(AnchorCommissionLog::class, 'anchor_id');
    }
}
