<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AnchorAccount extends Model
{
    use HasFactory;

    protected $table = 'dymcn_anchor_account';

    protected $fillable = [
        'anchor_id',
        'account_uid',
        'account_name',
        'account_status',
        'real_name',
        'phone',
        'id_card_no',
        'device',
        'certificate_no',
        'certificate_legal_person',
        'certificate_image',
        'total_commission',
        'returned_commission',
        // 'payment_commission',
        'remark',
    ];

    protected static function boot()
    {
        parent::boot();

        static::updating(function (AnchorAccount $anchorAccount) {
            // 判断 anchor_id 是否被修改
            if ($anchorAccount->isDirty('anchor_id')) {
                $newAnchorId = $anchorAccount->anchor_id;

                // 更新关联的 LiveData 模型的 anchor_id
                $anchorAccount->liveDatas()->update(['anchor_id' => $newAnchorId]);
            }
        });
    }


    /**
     * 获取拥有此 AnchorAccount 的主播 Anchor。
     */
    public function anchor(): BelongsTo
    {
        return $this->belongsTo(Anchor::class);
    }

    /*
     * 通过访问器, 先关联获取本账号的主播, 再获取主播的所属用户。
     */
    public function user()
    {
        return $this->anchor->user();
    }

    /*
     * 账号下有多个直播场次数据
     */
    public function liveDatas(): HasMany
    {
        return $this->hasMany(LiveData::class);
    }

    /*
     * 账号下有多个cookies数据
     */
    public function cookies(): HasMany
    {
        return $this->hasMany(Cookies::class, 'uuid', 'account_uid');
    }
    /*
     * 最新的cookies是否在线
     */
    public function cookies_latest_online_first()
    {
        return $this->cookies()->where('is_online', 1)->orderBy('keeptime', 'desc')->first();
    }


    //更新或者创建主播账号
    public static function updateOrCreateByBuyinAccountId($buyin_account_id, $user_name = '', $user_mobile = '', $account_avatar = '')
    {
        $anchor_account = AnchorAccount::where('account_uid', $buyin_account_id)->first();
        if ($anchor_account) {
            //如果原始电话号码中不包含*号,则使用原始电话号码
            if (strpos($anchor_account->phone, '*') === false) {
                $user_mobile = $anchor_account->phone;
            }
            $anchor_account->update([
                'account_name'   => $user_name,
                'phone'          => $user_mobile,
                'account_avatar' => $account_avatar,
            ]);
        } else {
            AnchorAccount::create([
                'account_uid'    => $buyin_account_id,
                'account_name'   => $user_name,
                'phone'          => $user_mobile,
                'account_avatar' => $account_avatar,
                'account_status' => '正常',
                'real_name' => '未知',
                'id_card_no' => '未知',
                'device' => '未知',
                'certificate_no' => '未知',
                'certificate_legal_person' => '未知',
                'certificate_image' => '',
                'margin_amount' => 0,
                'reputation_score' => 0,
                'credit_score' => 0,
                'price_score' => 0,
            ]);
        }
    }

}
