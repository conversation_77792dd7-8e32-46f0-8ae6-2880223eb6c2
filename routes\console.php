<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;
use \Spatie\Health\Commands\DispatchQueueCheckJobsCommand;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();


//每分钟运行一次此调度,以检查队列作业的健康状况
Schedule::command(DispatchQueueCheckJobsCommand::class)->everyMinute();
