<?php

namespace App\Filament\Resources\AnchorCommissionLogResource\Pages;

use App\Filament\Resources\AnchorCommissionLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAnchorCommissionLogs extends ListRecords
{
    protected static string $resource = AnchorCommissionLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()
                ->label('手动添加佣金记录'),
        ];
    }
}
