<?php

namespace App\Services;

use App\Models\AnchorAccount;
use App\Models\Cookies;
use Filament\Notifications\Notification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CookiesService
{

    //获取 cookies_str
    private static function get_cookies_str($cookies_json)
    {
        $cookies_str = '';
        $cookies_array = json_decode($cookies_json, true);
        foreach ($cookies_array as $cookie) {
            if (isset($cookie['name']) && isset($cookie['value'])) {
                $cookies_str .= $cookie['name'] . '=' . $cookie['value'] . ';';
            }
        }
        return $cookies_str;
    }


    //获取 s_v_web_id 的值
    private static function get_cookies_val_by_s_v_web_id($cookies_json)
    {
        $cookies_array = json_decode($cookies_json, true);
        foreach ($cookies_array as $cookie) {
            //如果$cookie['name']的值为 s_v_web_id ,则返回$cookie['value']的值
            if (isset($cookie['name']) && $cookie['name'] == 's_v_web_id') {
                return $cookie['value'];
            }
        }
        return '';
    }

    //获取 SASID 的值
    private static function get_cookies_val_by_SASID($cookies_json)
    {
        $cookies_array = json_decode($cookies_json, true);
        foreach ($cookies_array as $cookie) {
            //如果$cookie['name']的值为 s_v_web_id ,则返回$cookie['value']的值
            if (isset($cookie['name']) && isset($cookie['value']) && $cookie['name'] == 'SASID') {
                return $cookie['value'];
            }
        }
        return '';
    }

    //获取 msToken 的值
    private static function get_msToken($random_length = 0)
    {
        //随机生成字符串 $random_length 个字符
        $random_string = '';
        for ($i = 0; $i < $random_length; $i++) {
            $random_string .= chr(mt_rand(97, 122));
        }
        return $random_string;
    }


    //获取 LUOPAN_DT 的值
    private static function get_cookies_val_by_LUOPAN_DT($cookies_json)
    {
        $cookies_array = json_decode($cookies_json, true);
        foreach ($cookies_array as $cookie) {
            //如果$cookie['name']的值为 s_v_web_id ,则返回$cookie['value']的值
            if (isset($cookie['name']) && isset($cookie['value']) && $cookie['name'] == 'LUOPAN_DT') {
                return $cookie['value'];
            }
        }
        return '';
    }


    public static function checkCookies(Cookies $record, &$buyin_account_id = 0, &$user_name='', &$user_mobile='', &$account_avatar='')
    {
        $SASID = self::get_cookies_val_by_SASID($record->cookies);
        // $cookies_str = self::get_cookies_str($record->cookies);
        // $s_v_web_id = self::get_cookies_val_by_s_v_web_id($record->cookies);
        // $verifyFp = $s_v_web_id;
        // $fp = $s_v_web_id;
        // $msToken = self::get_msToken(128);
        // $a_bogus = self::get_msToken(172);
        // $url = "https://buyin.jinritemai.com/index/getUser?verifyFp={$verifyFp}&fp={$fp}&msToken={$msToken}&a_bogus={$a_bogus}";
        // //使用laravel的Http客户端
        // $web_code = Http::withHeaders([
        //     // 'User-Agent' => $record->useragent,
        //     'cookie'     => $cookies_str,
        //     "Referer"  => "https://buyin.jinritemai.com/dashboard/livedata/index?pre_universal_page_params_id=&universal_page_params_id=1e391c0d-9a7f-46c8-b5f7-3e7fe2241a7f",
        // ])->get($url)->body();

        $url = "https://buyin.jinritemai.com/index/getUser";
        //使用laravel的Http客户端
        $web_code = Http::withHeaders([
            // 'User-Agent' => $record->useragent,
            'cookie'  => "SASID={$SASID};",
            "Referer" => "https://buyin.jinritemai.com/dashboard/livedata/index",
        ])->get($url)->body();

        $web_code_array = json_decode($web_code, true);
        if (isset($web_code_array['data']) && isset($web_code_array['data']['buyin_account_id'])) {

            $user_name = $web_code_array['data']['user_name'];
            $user_mobile = $web_code_array['data']['user_mobile'];
            $account_avatar = $web_code_array['data']['account_avatar'];
            $buyin_account_id = $web_code_array['data']['buyin_account_id'];

            $result = true;
            $result_msg = "{$user_name}";

            AnchorAccount::updateOrCreateByBuyinAccountId($buyin_account_id, $user_name, $user_mobile, $account_avatar);
        } else {
            $result = false;
            $result_msg = str_replace("无效", "", $record->remark);
            $result_msg = trim($result_msg);
            $result_msg = "{$result_msg} 无效";
        }
        try {
            $record->update([
                'uuid'      => $buyin_account_id ?: $record->uuid,
                'is_online' => $result ? 1 : -1,
                'remark'    => $result_msg,
                'keeptime'  => Carbon::now()->timestamp,
            ]);
        } catch (\Exception $e) {
            $result = false;
            $result_msg = $e->getMessage();
        }

        return $result;
    }


    public static function getWebCode($url, $useragent="", $cookies="", $timeout=30, $show_window=1, $min_show_time=0)
    {
        $SASID = self::get_cookies_val_by_SASID($cookies);
        //使用laravel的Http客户端
        $web_code = Http::withHeaders([
            // 'User-Agent' => $useragent,
            'cookie'  => "SASID={$SASID};",
            "Referer" => "https://buyin.jinritemai.com/dashboard/livedata/index",
        ])->get($url)->body();
        $web_code_array = json_decode($web_code, true);
        return $web_code_array;
    }


    //获取web_json_code基于某个uuid的
    public static function getWebJsonCodeByUuid($uuid, $url)
    {
        $cookies_row = Cookies::where('uuid', $uuid)->where('is_online', 1)->orderBy('keeptime', 'asc')->first();
        //如果账号不存在,则报错
        if (!$cookies_row) {
            throw new \Exception('cookies不存在');
        }
        $web_code_array = self::getWebCode($url, $cookies_row->useragent, $cookies_row->cookies);
        return $web_code_array;
    }

    //获取web_json_code基于某个uuid的, 必须有 LUOPAN_DT
    public static function getWebJsonCodeByLUOPANDT($uuid, $url)
    {
        $cookies_row = Cookies::where('uuid', $uuid)->where('is_online', 1)
            ->whereLike('cookies', '%LUOPAN_DT%')
            ->orderBy('keeptime', 'asc')->first();
        //如果账号不存在,则报错
        if (!$cookies_row) {
            throw new \Exception('cookies不存在');
        }

        $LUOPAN_DT = self::get_cookies_val_by_LUOPAN_DT($cookies_row->cookies);

        //使用laravel的Http客户端
        $web_code = Http::withHeaders([
            // 'User-Agent' => $cookies_row->useragent,
            'cookie'  => "LUOPAN_DT={$LUOPAN_DT}",
            "Referer" => "https://buyin.jinritemai.com/dashboard/livedata/index",
        ])->get($url)->body();

        $web_code_array = json_decode($web_code, true);
        return $web_code_array;
    }
}
