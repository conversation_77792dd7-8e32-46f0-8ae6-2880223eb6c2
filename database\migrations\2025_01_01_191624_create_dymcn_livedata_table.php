<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dymcn_livedata', function (Blueprint $table) {
            $table->comment('直播数据');
            $table->bigIncrements('id');
            $table->integer('anchor_id')->default(0)->comment('所属主播ID');
            $table->integer('anchor_account_id')->default(0)->comment('所属账号ID');
            $table->string('room_id')->default('')->comment('场次ID');
            $table->string('base_info')->default('')->comment('直播标题');
            $table->dateTime('start_time')->comment('开始时间');
            $table->dateTime('end_time')->comment('结束时间');
            $table->bigInteger('duration_second')->default(0)->comment('持续秒');
            $table->integer('viewers')->default(0)->comment('观看人数');
            $table->integer('exposure')->default(0)->comment('商品曝光次数');
            $table->integer('clicks')->default(0)->comment('商品点击次数');
            $table->decimal('sales', 10, 2)->default(0)->comment('成交金额');
            $table->string('content_quality')->default('')->comment('内容质量');

            $table->decimal('total_commission', 10, 2)->default(0)->comment('累计佣金');
            $table->decimal('returned_commission', 10, 2)->default(0)->comment('累计已退佣');
            $table->decimal('real_commission', 10, 2)->default(0)->comment('实际佣金');
            $table->decimal('payment_commission', 10, 2)->default(0)->comment('累计已结算');

            $table->integer('is_completed_lock')->nullable()->default(0)->comment('是否完成锁定');
            $table->text('remark')->nullable()->comment('备注信息');

            $table->bigInteger('avg_watch_duration')->default(0)->comment('平均观看时长(秒)');
            $table->integer('fans_club_ucnt')->default(0)->comment('新增直播团人数');
            $table->bigInteger('gmv')->default(0)->comment('客单价(分)');
            $table->bigInteger('gpm')->default(0)->comment('客单价(分)');
            $table->integer('incr_fans_cnt')->default(0)->comment('新增粉丝数');
            $table->integer('online_user_cnt')->default(0)->comment('平均在线人数');
            $table->integer('online_user_ucnt')->default(0)->comment('累计观看人数');
            $table->integer('pay_cnt')->default(0)->comment('成交件数');
            $table->decimal('pay_fans_ratio', 10, 4)->default(0)->comment('成交粉丝占比');
            $table->integer('pay_ucnt')->default(0)->comment('成交人数');
            $table->decimal('product_click_to_pay_rate', 10, 4)->default(0)->comment('点击-成交转化率');
            $table->json('flow_order_source')->nullable()->comment('流量分析数据');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dymcn_livedata');
    }
};
