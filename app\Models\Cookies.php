<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cookies extends Model
{
    use HasFactory;

    protected $table = 'dymcn_cookies';

    protected $fillable = [
        'uuid',
        'domain',
        'useragent',
        'cookies',
        'is_online',
        'keeptime',
        'remark',
    ];

    protected $casts = [
    ];


    // 在线类型
    public static function getIsOnlineTypeList()
    {
        return [
            '-1' => '不在线',
            '0' => '未知',
            '1' => '在线',
        ];
    }

    /**
     * 所属账号
     */
    public function anchorAccount(): BelongsTo
    {
        return $this->belongsTo(anchorAccount::class, 'uuid', 'account_uid');
    }
}
