<?php

namespace App\Filament\Resources\LiveDataCommissionResource\Pages;

use App\Filament\Resources\LiveDataCommissionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewLiveDataCommission extends ViewRecord
{

    protected static string $resource = LiveDataCommissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

}
