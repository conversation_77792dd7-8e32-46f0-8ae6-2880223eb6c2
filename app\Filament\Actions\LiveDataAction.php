<?php

namespace App\Filament\Actions;

use App\Models\LiveData;


use App\Models\LiveDataCommission;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;

// 别名命名空间
use Filament\Tables\Actions\Action as TableAction;
use Filament\Actions\Action as FilamentAction;
use Illuminate\Support\Facades\Auth;


class LiveDataAction
{
    /**
     * 获取操作类实例
     *
     * @param string $actionType
     * @return \Filament\Tables\Actions\Action|\Filament\Actions\Action
     */
    private static function getActionInstance(string $actionType, string $action_name)
    {
        if ($actionType === 'Tables') {
            return TableAction::make($action_name);
        }

        return FilamentAction::make($action_name);
    }


    public static function actionCommission(string $actionType = "",string $reqType, string $actionLabel="")
    {
        if (empty($actionLabel)) {
            $reqTypeList =  LiveDataCommission::getReqTypeList();
            if (isset($reqTypeList[$reqType])) {
                $actionLabel =  $reqTypeList[$reqType];
            }else{
                $actionLabel = '不支持的类型';
            }
        }
        $action = self::getActionInstance($actionType, $reqType);
        return $action->label($actionLabel)
            ->disabled(fn(LiveData $record) => $record->is_completed_lock)
            // ->hidden(fn(LiveData $record) => $record->real_commission == $record->payment_commission && $record->real_commission>0)
            ->beforeFormFilled(function (LiveData $record, $data) {
                if ($record->is_completed_lock) {
                    Notification::make()
                        ->title('错误')
                        ->body('本场数据已被置为完成锁定状态,不可修改!')
                        ->warning()
                        ->send();
                    // 停止继续执行
                    return false;
                }
            })
            ->form([
                Placeholder::make('id')
                    ->label("直播场次")
                    ->hiddenLabel()
                    ->content(fn(LiveData $record) => "{$record->start_time} - {$record->end_time}  成交金额:{$record->sales} {$record->base_info}")
                    //允许html
                    ->columnSpanFull(),

                \Filament\Forms\Components\Grid::make(3)
                    ->schema([
                        Placeholder::make('total_commission')
                            ->label('累计佣金')
                            ->content(fn(LiveData $record) => $record->total_commission),
                        Placeholder::make('returned_commission')
                            ->label('累计已退佣')
                            ->content(fn(LiveData $record) => $record->returned_commission),
                        Placeholder::make('real_commission')
                            ->label('实际佣金')
                            ->content(fn(LiveData $record) => $record->real_commission),
                        // Placeholder::make('payment_commission')
                        //     ->label('累计已结算')
                        //     ->content(fn(LiveData $record) => $record->payment_commission),
                    ]),

                TextInput::make('req_amount')
                    ->label("操作佣金金额数量")
                    ->required()
                    ->numeric(),
                Textarea::make('req_remark')
                    ->label('备注描述')
                    ->columnSpanFull(),
                //req_images 多文件上传
                FileUpload::make('req_images')
                    ->label('备注图片')
                    ->helperText('您可以在这里上传本次佣金的付款到账的截图或者其他相关凭证.')
                    ->multiple()
                    ->directory('livedata')
                    ->image()
                    ->imagePreviewHeight('250')
                    ->minFiles(0)
                    ->maxFiles(10),
            ])
            ->action(function (array $data, LiveData $record) use($reqType, $action) {

                //金额不得小于0
                if ($data['req_amount'] < 0) {
                    Notification::make()
                        ->title('错误')
                        ->body('操作金额不得小于0!')
                        ->warning()
                        ->send();
                    // 停止继续执行
                    $action->halt();
                }

                if ($reqType == 'refund_commission') {
                    //退佣金额不得大于已存在的佣金金额
                    if ($data['req_amount'] > $record->real_commission) {
                        Notification::make()
                            ->title('错误')
                            ->body("退佣金额{$data['req_amount']}超过了实际佣金金额{$record->real_commission}!")
                            ->warning()
                            ->send();
                        // 停止继续执行
                        $action->halt();
                    }
                }

                // if ($reqType == 'payment_commission') {
                //     //退佣金额不得大于已存在的佣金金额
                //     if ($data['req_amount'] > $record->real_commission) {
                //         Notification::make()
                //             ->title('错误')
                //             ->body("打款金额{$data['req_amount']}超过了实际佣金金额{$record->real_commission}!")
                //             ->warning()
                //             ->send();
                //         // 停止继续执行
                //         $action->halt();
                //     }
                // }

                //数据将保存到关联表
                $record->liveDataCommissions()->create([
                    'req_type' => $reqType,
                    'req_username' => Auth::user()->name,
                    'req_amount' => $data['req_amount'],
                    'req_remark' => $data['req_remark'],
                    'req_images' => $data['req_images'],
                ]);


                Notification::make()
                    ->title('成功')
                    ->body('已提交本记录!')
                    ->success()
                    ->send();
                // 使用 Livewire 的 redirect 方法刷新页面
                return redirect(request()->header('Referer'));
            });
    }

    // 获取直播场次的高级数据
    public static function actionGetLivedataDetail(string $actionType = "",string $reqType, string $actionLabel=""){
        $action = self::getActionInstance($actionType, $reqType);
        return $action->label($actionLabel)
            ->action(function (array $data, LiveData $record) {
                try{
                    $anchor_account = \App\Models\AnchorAccount::where('id', $record->anchor_account_id)->first();
                    if (!$anchor_account) {
                        throw new \Exception('账号不存在');
                    }

                    // 获取直播场次的高级数据
                    $livedata_detail = \App\Services\BuyinService::get_livedata_detail($anchor_account->account_uid, $record->room_id);
                    // dump($livedata_detail);
                    // 提示
                    Notification::make()
                        ->title('成功')
                        ->body('获取高级统计数据成功!')
                        ->success()
                        ->send();
                }catch(\Exception $e){
                    Notification::make()
                        ->title('错误')
                        ->body('获取高级统计数据失败!'.$e->getMessage())
                        ->warning()
                        ->send();
                }
            });
    }
}
