<?php

namespace App\Jobs;

use App\Models\Cookies;
use App\Services\CookiesService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class CheckCookiesIsOnlineJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $cookie;

    /**
     * 任务最大重试次数
     */
    public $tries = 2;

    /**
     * 任务可以执行的最大秒数
     */
    public $timeout = 30;

    /**
     * 重试之间的等待秒数
     */
    public $backoff = [10, 30, 60];

    /**
     * Create a new job instance.
     */
    public function __construct(Cookies $cookie)
    {
        $this->cookie = $cookie;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $result = CookiesService::checkCookies($this->cookie);
            if ($result) {
                echo "Cookies {$this->cookie->uuid} 在线 ".Carbon::now()->toDateTimeString().PHP_EOL;
            } else {
                echo "Cookies {$this->cookie->uuid} 已离线 ".Carbon::now()->toDateTimeString().PHP_EOL;
            }
        } catch (\Exception $e) {
            Log::error("检查Cookies失败: {$this->cookie->uuid}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // 如果重试次数已达到最大值,则将cookie标记为离线
            if ($this->attempts() >= $this->tries) {
                $this->cookie->update([
                    'is_online' => -1,
                    'remark' => '检查失败: ' . $e->getMessage(),
                    'keeptime' => Carbon::now()->timestamp,
                ]);
            } else {
                throw $e; // 抛出异常触发重试
            }
        }
    }
}
