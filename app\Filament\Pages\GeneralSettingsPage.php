<?php

namespace App\Filament\Pages;

use App\Settings\GeneralSettings;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class GeneralSettingsPage extends SettingsPage
{
    use HasPageShield;

    protected static ?string $title =  "基础设置";
    protected static ?string $navigationLabel = "基础设置";
    protected ?string $heading = '基础设置';
    protected static ?string $navigationGroup = '设置';

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $settings = GeneralSettings::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([

                Forms\Components\Toggle::make('site_active')
                    ->inline(false)
                    ->label('站点是否开放')
                    ->default(true),

                Forms\Components\TextInput::make('site_name')
                    ->label('站点名称')
                    ->required()
                    ->default('DYMCN系统'),

                Forms\Components\TextInput::make('qq')
                    ->label('QQ')
                    ->required()
                    ->default('22707370'),

                Forms\Components\TextInput::make('weixin')
                    ->label('微信')
                    ->required()
                    ->default('ttmm_vip'),

                Forms\Components\TextInput::make('email')
                    ->label('邮箱')
                    ->required()
                    ->default('<EMAIL>'),

                Forms\Components\TextInput::make('icp')
                    ->label('ICP备案号')
                    ->required()
                    ->default('ICP备2024000000号'),


                Forms\Components\TextInput::make('large_screen_title')
                    ->label('大屏主标题')
                    ->required()
                    ->columnSpanFull()
                    ->default('同创直播数据平台'),


                Forms\Components\TextInput::make('goods_sale_base')
                    ->label('基础累计销售额(单位:万)')
                    ->placeholder('在此基础上会加上系统中所抓取到的数据作为展示')
                    ->numeric()
                    ->required()
                    ->default(0),

                Forms\Components\TextInput::make('goods_view_base')
                    ->label('基础累计商品展示次(单位:万)')
                    ->placeholder('在此基础上会加上系统中所抓取到的新数据作为展示')
                    ->numeric()
                    ->required()
                    ->default(0),

                Forms\Components\TextInput::make('live_number_base')
                    ->label('基础累计直播场次(单位:次)')
                    ->placeholder('在此基础上会加上系统中所抓取到的新数据作为展示')
                    ->numeric()
                    ->required()
                    ->default(0),
                Forms\Components\TextInput::make('live_hour_base')
                    ->label('基础累计直播时长(单位:小时)')
                    ->placeholder('在此基础上会加上系统中所抓取到的新数据作为展示')
                    ->numeric()
                    ->required()
                    ->default(0),

                Forms\Components\TextInput::make('all_number_people')
                    ->label('展示员工总数量(单位:人)')
                    ->placeholder('直接使用此数值进行展示')
                    ->numeric()
                    ->required()
                    ->default(0),
                Forms\Components\TextInput::make('all_number_anchor')
                    ->label('展示主播总数量(单位:人)')
                    ->placeholder('直接使用此数值进行展示')
                    ->numeric()
                    ->required()
                    ->default(0),
            ]);
    }
}
