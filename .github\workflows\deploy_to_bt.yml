
name: 部署到宝塔面板

on:
  push:
    branches:
      - main


jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
        - name: 检出代码
          uses: actions/checkout@v4

        - name: 设置PHP版本
          uses: shivammathur/setup-php@v2
          with:
            php-version: 8.2

        - name: 验证PHP版本
          run: php -v

        - name: 更新 .env 配置文件
          run: |
            cp .env.example .env
            touch .env

            sed -i '/^APP_URL=/d' .env
            echo "APP_URL=https://dymcn-laravel.ttmm.vip" >> .env

            sed -i '/^DB_CONNECTION=/d' .env
            echo "DB_CONNECTION=mysql" >> .env

            sed -i '/^DB_HOST=/d' .env
            echo "DB_HOST=sh-cynosdbmysql-grp-d2wa9pi6.sql.tencentcdb.com" >> .env

            sed -i '/^DB_PORT=/d' .env
            echo "DB_PORT=21077" >> .env

            sed -i '/^DB_DATABASE=/d' .env
            echo "DB_DATABASE=dymcn_laravel" >> .env

            sed -i '/^DB_USERNAME=/d' .env
            echo "DB_USERNAME=dymcn_laravel" >> .env

            sed -i '/^DB_PASSWORD=/d' .env
            echo "DB_PASSWORD=ekKnGtYDrA5t8Mbj" >> .env


        - name: 安装composer依赖
          run: composer install

        - name: 列出当前目录路径
          run: pwd

        - name: 部署到服务器
          uses: easingthemes/ssh-deploy@main
          with:
            SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
            ARGS: "-rlgoDzvc -i"
            SOURCE: "./"
            REMOTE_HOST: ${{ vars.REMOTE_HOST }}
            REMOTE_PORT: ${{ vars.REMOTE_PORT }}
            REMOTE_USER: ${{ vars.REMOTE_USER }}
            TARGET: "/www/wwwroot/dymcn-laravel.ttmm.vip/prod_web"
            EXCLUDE: "/.git/, /vendor/"
            SCRIPT_BEFORE: |
              cp -nr /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/.env /www/wwwroot/dymcn-laravel.ttmm.vip/.env
              rm -rf /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/*
            SCRIPT_AFTER: |
              cp -nr /www/wwwroot/dymcn-laravel.ttmm.vip/.env /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/.env
              chown -R www:www /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/
              chmod 755 -R /www/wwwroot/dymcn-laravel.ttmm.vip/prod_web/

