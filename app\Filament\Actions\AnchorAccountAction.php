<?php

namespace App\Filament\Actions;

use App\Models\AnchorAccount;


use App\Models\AnchorAccountCommission;
use App\Services\BuyinService;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;

// 别名命名空间
use Filament\Tables\Actions\Action as TableAction;
use Filament\Actions\Action as FilamentAction;
use Illuminate\Support\Facades\Auth;

class AnchorAccountAction
{
    /**
     * 获取操作类实例
     *
     * @param string $actionType
     * @return \Filament\Tables\Actions\Action|\Filament\Actions\Action
     */
    private static function getActionInstance(string $actionType, string $action_name)
    {
        if ($actionType === 'Tables') {
            return TableAction::make($action_name);
        }

        return FilamentAction::make($action_name);
    }


    public static function refreshAccount(string $actionType = "", string $action_name, string $actionLabel)
    {
        $action = self::getActionInstance($actionType, $action_name);
        return $action->label($actionLabel)
            ->disabled(fn(AnchorAccount $record) => $record->is_completed_lock)
            // ->hidden(fn(AnchorAccount $record) => $record->real_commission == $record->payment_commission && $record->real_commission > 0)
            ->beforeFormFilled(function (AnchorAccount $record, $data) {
                if ($record->is_completed_lock) {
                    Notification::make()
                        ->title('错误')
                        ->body('本场数据已被置为完成锁定状态,不可修改!')
                        ->warning()
                        ->send();
                    // 停止继续执行
                    return false;
                }
            })
            ->action(function (array $data, AnchorAccount $record) use ($action) {
                try {
                    BuyinService::refreshAccount($record->account_uid);
                    Notification::make()
                        ->title('操作成功')
                        ->body("已刷新账户数据")
                        ->success()
                        ->send();
                } catch (\Exception $e) {
                    Notification::make()
                        ->title('操作失败')
                        ->body("异常:{$e->getMessage()}")
                        ->danger()
                        ->send();
                }
                // 使用 Livewire 的 redirect 方法刷新页面
                redirect(request()->header('Referer'));
            });
    }


    public static function refreshLivedata(string $actionType = "", string $action_name, string $actionLabel)
    {
        $action = self::getActionInstance($actionType, $action_name);
        return $action->label($actionLabel)
            ->disabled(fn(AnchorAccount $record) => $record->is_completed_lock)
            // ->hidden(fn(AnchorAccount $record) => $record->real_commission == $record->payment_commission && $record->real_commission > 0)
            ->beforeFormFilled(function (AnchorAccount $record, $data) {
                if ($record->is_completed_lock) {
                    Notification::make()
                        ->title('错误')
                        ->body('本场数据已被置为完成锁定状态,不可修改!')
                        ->warning()
                        ->send();
                    // 停止继续执行
                    return false;
                }
            })
            ->action(function (array $data, AnchorAccount $record) use ($action) {
                try {
                    $livedata_list_count = BuyinService::refreshLivedata($record->account_uid);
                    Notification::make()
                        ->title('操作成功')
                        ->body("本次刷新获取到直播场次{$livedata_list_count}场[可能含之前的数据]")
                        ->success()
                        ->send();
                } catch (\Exception $e) {
                    Notification::make()
                        ->title('操作失败')
                        ->body("异常:{$e->getMessage()}")
                        ->danger()
                        ->send();
                }
                // 使用 Livewire 的 redirect 方法刷新页面
                redirect(request()->header('Referer'));
            });
    }
}
