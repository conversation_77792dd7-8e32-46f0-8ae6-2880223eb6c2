<?php

namespace App\Filament\Pages;

use Filament\Pages\Page;
use Illuminate\Support\Str;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;

class HelpDocumentationPage extends Page
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel = '系统帮助文档';
    protected static ?string $navigationGroup = '设置';
    protected static ?string $title = '系统帮助文档';
    protected ?string $heading = '系统帮助文档';
    protected static ?int $navigationSort = 100;



    protected static string $view = 'filament.pages.help-documentation-page';

    public string $activeTab = 'general';



    protected function getViewData(): array
    {
        return [
            'generalHelp' => $this->getGeneralHelp(),
            'superAdminHelp' => $this->getSuperAdminHelp(),
            'assistantHelp' => $this->getAssistantHelp(),
            'businessHelp' => $this->getBusinessHelp(),
            'financeHelp' => $this->getFinanceHelp(),
            'systemHelp' => $this->getSystemHelp(),
        ];
    }

    private function getGeneralHelp(): string
    {
        return Str::markdown('
### 通用管理员(所有身份)

- 登录账号，进入后台后，右上角可以编辑个人信息，修改密码
- 在列表页面，可以勾选多条记录，然后批量操作或者导出
- 如果希望勾选全部，是在勾选后，列表的右上方有一个勾选全部记录

### 关于 Daya-Cookie-Cloud 插件

此平台需要结合 Daya-Cookie-Cloud 这个浏览器插件来使用：
- 该插件会主动获取直播平台的账号信息，然后自动同步到系统中
- 浏览器安装此插件后，需要登录到主播账号上
- 然后进入主播的直播罗盘页面，这样平台就会自动获取到该账号的身份信息
- 获取到主播账号后，大概30分钟内主播账号在平台会变成在线状态
- 可以将某一个主播账号归属于某一个助理用户
        ');
    }

    private function getSuperAdminHelp(): string
    {
        return Str::markdown('
### 超级管理员

- 拥有后台所有的功能
- 可以管理所有功能，包括各个角色身份的权限功能
- 注意：权限功能一旦设置正常后，就不要去乱调整设置，否则会导致其他角色看到敏感数据
        ');
    }

    private function getAssistantHelp(): string
    {
        return Str::markdown('
### 助理用户组

1. 添加主播
2. 将浏览器插件安装好后，登录某个主播账号
3. 待平台主播账号在线后，将该主播账号归属于自己的账号中
4. 可以经常检查主播的cookies是否在线，如果不在线后，需要在浏览器登录主播账号，并进入主播的直播罗盘页面，5分钟左右平台才能将主播账号置为在线
5. 可以在主播列表的最右侧，点击3个点按钮，可以打开扩展菜单，可以刷新主播账号数据，可以更新该主播的直播场次列表
6. 如果希望及时的让主播账号在线，可以点击Cookies列表，将状态是"未知"的Cookies手动检查Cookies，可以实时刷新Cookies状态
7. 可以手动刷新某个主播账号的直播场次数据
   - 注意：该直播场次数据每次都只拉取最近的10场直播，意味着几天之前的数据都不会被拉取
        ');
    }

    private function getBusinessHelp(): string
    {
        return Str::markdown('
### 商务用户组

1. 助理刷新了直播场次后，商务用户可以看到直播场次列表，可以在这里给某个主播的某个场次添加佣金记录或添加退佣记录
2. 添加佣金或退佣申请时候，可以填写备注信息，也可以上传转账凭证
3. 可以查看佣金列表，某些佣金记录可能被财务所拒绝，可以查看拒绝原因
        ');
    }

    private function getFinanceHelp(): string
    {
        return Str::markdown('
### 财务用户组

1. 当商务添加了佣金或退佣申请后，财务用户可以在这里看到佣金或退佣申请列表，可以审批佣金或退佣申请
2. 在审批的时候，可以填写通过原因或者拒绝原因
3. 直播佣金记录页面：
   - 此页面的佣金是归属于某一个直播场次的
   - 时间范围筛选的按照该场次直播时间来筛选的
4. 主播佣金统计：
   - 这直接是主播的某个时间范围内的佣金合计
   - 时间筛选范围，是按照佣金审批的时间来筛选的
   - 比如想进行某月的所有主播对账，可以使用这个功能
5. 主播佣金记录：
   - 此页面的佣金记录是所有主播的佣金变动明细
   - 时间筛选范围，是按照佣金审批的时间来筛选的
   - 比如想进行某月的主播金额操作变化明细，可以使用此功能
   - 这个就是主播佣金统计页面的数据来源
        ');
    }

    private function getSystemHelp(): string
    {
        return Str::markdown('
### 系统后台自动维护

1. 系统会自动维护主播的cookies状态
   - 浏览器插件会自动上传符合条件的Cookies
   - 系统会自动将Cookies状态置为在线
   - 预计每隔30分钟内自动维护一次

2. 系统会自动维护主播的直播场次数据
   - 如果主播账号的Cookies在线，则系统会自动将主播账号的直播场次数据拉取到系统中
   - 预计每隔30分钟内自动维护一次

3. 系统会自动刷新主播账号的分值数据
   - 主播账号有信用分等等
   - 每个账号系统预计每24小时内自动维护一次

4. 系统会刷新直播场次的罗盘数据
   - 当拉取到了最新的直播场次数据后且该场次的罗盘数据没有更新
   - 则系统会自动刷新该场次的罗盘数据
   - 预计是拉取场次后的30分钟内自动拉取

### 提示
为了系统的稳定和服务器性能考虑，系统自动维护并不会高频次的去拉取数据，如果希望数据及时更新，可以手动刷新相关功能。
        ');
    }
}
