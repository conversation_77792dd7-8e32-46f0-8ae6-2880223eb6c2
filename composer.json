{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "bezhansalleh/filament-shield": "^3.3", "chrome-php/chrome": "^1.11", "filament/filament": "^3.2", "filament/spatie-laravel-settings-plugin": "^3.2", "jeffgreco13/filament-breezy": "^2.6", "laravel/framework": "^11.0", "laravel/horizon": "^5.25", "laravel/octane": "^2.5", "laravel/tinker": "^2.9", "maatwebsite/excel": "^3.1", "php-webdriver/webdriver": "^1.15", "psr/simple-cache": "^2.0", "rmsramos/activitylog": "^1.0", "shuvroroy/filament-spatie-laravel-health": "^2.0", "spiral/roadrunner-cli": "^2.6.0", "spiral/roadrunner-http": "^3.3.0", "staudenmeir/eloquent-has-many-deep": "^1.20", "stechstudio/filament-impersonate": "^3.8"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.13", "fakerphp/faker": "^1.23", "kitloong/laravel-migrations-generator": "^7.0", "laravel-lang/attributes": "^2.10", "laravel-lang/common": "^6.2", "laravel-lang/lang": "^15.4", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"0": {"type": "vcs", "url": "https://github.com/damahaokaixin/filament-approvals.git"}}}