<?php

namespace App\Providers;

use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

use Spatie\Health\Facades\Health;
use Spatie\Health\Checks\Checks\HorizonCheck;
use Spatie\Health\Checks\Checks\QueueCheck;
use Spatie\Health\Checks\Checks\UsedDiskSpaceCheck;
use Spatie\Health\Checks\Checks\OptimizedAppCheck;
use Spatie\Health\Checks\Checks\DebugModeCheck;
use Spatie\Health\Checks\Checks\EnvironmentCheck;
use Spatie\Health\Checks\Checks\ScheduleCheck;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Gate::policy(\RingleSoft\LaravelProcessApproval\Models\ProcessApprovalFlow::class, \App\Policies\ProcessApprovalFlowPolicy::class);

        Health::checks([
            OptimizedAppCheck::new(),
            DebugModeCheck::new(),
            EnvironmentCheck::new(),

            // 检测磁盘空间, 有时候会出现磁盘空间不足的情况, 可能是路径问题, 屏蔽掉.
            // UsedDiskSpaceCheck::new(),


            // 检测调度任务队列 需要在调度计划中加入一条1分钟的命令
            QueueCheck::new(),

            // 检测计划任务, 需要在调度计划中加入一条1分钟的命令
            ScheduleCheck::new(),


            // 检测 Horizon 状态
            HorizonCheck::new(),
        ]);

    }
}
