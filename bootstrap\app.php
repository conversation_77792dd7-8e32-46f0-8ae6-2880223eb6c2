<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withSchedule(function(\Illuminate\Console\Scheduling\Schedule $schedule){
        //每5分钟触发检测cookies是否在线
        $schedule->command(\App\Console\Commands\ScheduleCheckCookiesIsOnline::class)->everyFiveMinutes();
        // 每30分钟检测一次直播场次数据
        $schedule->command(\App\Console\Commands\ScheduleCheckLiveData::class)->everyThirtyMinutes();
        // 健康检查的调度任务, 需要每隔1分钟执行一次
        $schedule->command(\Spatie\Health\Commands\ScheduleCheckHeartbeatCommand::class)->everyMinute();
        // 队列检查, 需要每隔1分钟执行一次
        $schedule->command(\Spatie\Health\Commands\DispatchQueueCheckJobsCommand::class)->everyMinute();
    })
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->validateCsrfTokens(except: [
            '/addons/dymcn/*',
            '/dymcn/*',
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
