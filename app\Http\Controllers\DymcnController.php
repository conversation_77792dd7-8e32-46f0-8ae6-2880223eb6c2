<?php

namespace App\Http\Controllers;

use App\Models\Cookies;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DymcnController extends Controller
{

    // 保存cookies
    // https://dymcn-laravel.ttmm.vip/addons/dymcn/cookies
    // http://dymcn.test/addons/dymcn/cookies
    public function cookies(Request $request)
    {
        // 获取 POST 请求中的json数据包
        $jsonData = $request->json()->all();

        $download = $request->input('download');
        if ($download) {
            //用id desc倒序取出一条
            $cookies_row = Cookies::where('domain', $jsonData['domain'])->where('is_online', 1)->orderBy('id', 'desc')->first();

            return response()->json(
                [
                    'code' => 1,
                    'msg'  => '数据获取成功',
                    'data' => json_decode($cookies_row['cookies'], true)
                ],
                200 , [], JSON_UNESCAPED_UNICODE);
        }

        // 获取 POST 请求中的json数据包
        $jsonData = $request->json()->all();
        // 数据验证
        $validator = Validator::make($jsonData, [
            'domain'    => 'required|string',
            'useragent' => 'required|string',
            'cookies'   => 'required|array',
            'taburl'    => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 0,
                'msg'  => '数据格式错误',
                'data' => ""
            ], 400, [], JSON_UNESCAPED_UNICODE);
        }
        $uuid=$jsonData['domain'];

        //是否包含 LUOPAN_DT 这个KEY, 一定要包含才进行入库
        $is_has_luopan_dt=false;
        //遍历$jsonData['cookies']中的每一项
        foreach ($jsonData['cookies'] as $key => $value) {
            if ($value['domain'] == 'buyin.jinritemai.com' && $value['name'] == 'SASID'){
                $uuid=$value['value'];
            }
            if ($value['domain'] == '.buyin.jinritemai.com' && $value['name'] == 'SASID'){
                $uuid=$value['value'];
            }
            if ($value['name'] == 'LUOPAN_DT'){
                $is_has_luopan_dt=true;
            }
        }
        // 如果不包含, 则进行返回
        if (!$is_has_luopan_dt) {
            return response()->json(
                [
                    'code' => 0,
                    'msg'  => '不包含罗盘KEY',
                    'data' => ""
                ],
                200 , [], JSON_UNESCAPED_UNICODE);
        }

        //删除 $uuid 其他的状态是0的数据
        Cookies::where('uuid', $uuid)->where('is_online', 0)->delete();

        // 将数据存储到数据库Cookies表,一次性存储
        Cookies::create([
            'uuid'      => $uuid,
            'taburl'    => $jsonData['taburl'],
            'useragent' => $jsonData['useragent'],
            'domain'    => $jsonData['domain'],
            'cookies'   => json_encode($jsonData['cookies']),
            'is_online' => 0, //默认为状态未知
            'keeptime'  => time(),
        ]);
        return response()->json(
            [
                'code' => 1,
                'msg'  => '数据存储成功',
                'data' => ""
            ],
            200 , [], JSON_UNESCAPED_UNICODE);
    }
}
