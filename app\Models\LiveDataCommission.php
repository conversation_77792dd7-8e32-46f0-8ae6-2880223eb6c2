<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Auth;

class LiveDataCommission extends Model
{
    use HasFactory;

    protected $table = 'dymcn_livedata_commission';

    protected $fillable = [
        'livedata_id',
        'req_type',
        'req_username',
        'req_amount',
        'req_remark',
        'req_images',
        'approval_status',
        'approver_name',
        'approval_remark',
    ];

    protected $casts = [
        //多图片上传, 需要强制为数组模式
        'req_images' => 'array',
    ];

    // 请求类型
    public static function getReqTypeList()
    {
        return [
            'add_commission' => '添加佣金',
            'refund_commission' => '佣金退佣',
            // 'payment_commission' => '佣金打款',
        ];
    }

    // 审批状态
    public static function getApprovalStatusList()
    {
        return [
            'pending' => '待审核',
            'approved' => '已通过',
            'rejected' => '已拒绝',
        ];
    }


    /**
     * 所属场次
     */
    public function liveData(): BelongsTo
    {
        return $this->belongsTo(LiveData::class, 'livedata_id', 'id');
    }

    /**
     * 审批通过
     */
    public function approve(string $approverName, string | null $remark = ''): bool
    {
        if ($this->approval_status !== 'pending') {
            throw new \Exception('当前记录状态不允许审批');
        }

        //获取当前所属场次
        $liveData = $this->liveData;
        $anchor = $liveData->anchor;

        switch ($this->req_type) {
            case 'add_commission':
                //累计佣金
                $liveData->total_commission += $this->req_amount;
                //实际佣金
                $liveData->real_commission += $this->req_amount;
                //----------------
                //累计佣金
                $anchor->total_commission += $this->req_amount;
                //实际佣金
                $anchor->real_commission += $this->req_amount;

                // 创建佣金变动记录
                AnchorCommissionLog::create([
                    'anchor_id' => $anchor->id,
                    'anchor_account_id' => $liveData->anchor_account_id,
                    'livedata_id' => $liveData->id,
                    'commission_id' => $this->id,
                    'change_type' => 'add_commission',
                    'change_amount' => $this->req_amount,
                    'operator' => $approverName,
                    'remark' => $this->req_remark,
                ]);
                break;
            case 'refund_commission':
                //累计已退佣金
                $liveData->returned_commission += $this->req_amount;
                //实际佣金
                $liveData->real_commission -= $this->req_amount;
                //----------------
                //累计已退佣金
                $anchor->returned_commission += $this->req_amount;
                //实际佣金
                $anchor->real_commission -= $this->req_amount;

                // 创建佣金变动记录
                AnchorCommissionLog::create([
                    'anchor_id' => $anchor->id,
                    'anchor_account_id' => $liveData->anchor_account_id,
                    'livedata_id' => $liveData->id,
                    'commission_id' => $this->id,
                    'change_type' => 'refund_commission',
                    'change_amount' => -$this->req_amount,
                    'operator' => $approverName,
                    'remark' => $this->req_remark,
                ]);
                break;
            default:
                throw new \Exception('未知的佣金操作类型');
        }

        $liveData->save();
        $anchor->save();

        $this->update([
            'approval_status' => 'approved',
            'approver_name' => $approverName,
            'approval_remark' => $remark,
        ]);

        return true;
    }

    /**
     * 审批拒绝
     */
    public function reject(string $approverName, string $remark = ''): bool
    {
        if ($this->approval_status !== 'pending') {
            throw new \Exception('当前记录状态不允许审批');
        }

        $this->update([
            'approval_status' => 'rejected',
            'approver_name' => $approverName,
            'approval_remark' => $remark,
        ]);

        return true;
    }

    /**
     * 是否可以审批
     */
    public function canApprove(): bool
    {
        return $this->approval_status === 'pending';
    }

    protected static function boot()
    {
        parent::boot();

        // 创建时自动设置申请人和审批状态
        static::creating(function ($model) {
            if (empty($model->req_username)) {
                $model->req_username = Auth::user()->name ?? 'System';
            }
            if (empty($model->approval_status)) {
                $model->approval_status = 'pending';
            }
        });
    }
}
