<?php

namespace App\Filament\Resources\AnchorAccountResource\Pages;

use App\Filament\Actions\AnchorAccountAction;
use App\Filament\Resources\AnchorAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewAnchorAccount extends ViewRecord
{
    protected static string $resource = AnchorAccountResource::class;




    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),



            AnchorAccountAction::refreshLivedata("","refreshLivedata","刷新直播场次数据"),
            AnchorAccountAction::refreshAccount("","refreshAccount","刷新账号分值数据"),

        ];
    }


}
