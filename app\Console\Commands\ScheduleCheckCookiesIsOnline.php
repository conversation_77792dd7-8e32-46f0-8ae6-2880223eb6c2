<?php

namespace App\Console\Commands;

use App\Jobs\CheckCookiesIsOnlineJob;
use App\Models\Cookies;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class ScheduleCheckCookiesIsOnline extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:schedule-check-cookies-is-online';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '调度运行检测cookies是否在线';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //输出当前时间
        echo Carbon::now().PHP_EOL;
        echo "PHP调度被执行".PHP_EOL;
        echo "------------------------".PHP_EOL;
        //  `is_online` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否在线:-1=不在线,0=未知,1=在线'
        // 获取需要检查的cookies, 如果是未知的, 马上推送到队列
        // 获取需要检查的cookies, 如果是在线的,则值检测5分钟之前的数据
        $cookies = Cookies::where(function ($query) {
            $query->where('is_online', 0);
            $query->orWhere(function ($query) {
                $query->where('is_online', 1);
                $query->where('updated_at', '<', Carbon::now()->subMinutes(5));
            });
        })->get();

        // 分批处理,每批20个
        $chunks = $cookies->chunk(20);
        foreach ($chunks as $chunk) {
            foreach ($chunk as $cookie) {
                // 添加0-20秒的随机延迟,避免同时执行
                $delay = rand(0, 20);
                CheckCookiesIsOnlineJob::dispatch($cookie)->delay(now()->addSeconds($delay));
            }
            // 每批之间暂停1秒,避免瞬间推送太多任务
            sleep(1);
        }
    }
}
