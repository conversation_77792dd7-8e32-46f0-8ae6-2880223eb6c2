<?php

namespace App\Services;

use App\Models\AnchorAccount;
use App\Models\LiveData;
use Illuminate\Support\Str;


class BuyinByWebViewService
{

    //刷新账号
    public static function refreshAccount($account_uid)
    {
        $anchor_account = AnchorAccount::where('account_uid', $account_uid)->first();
        if (!$anchor_account) {
            throw new \Exception('账号不存在');
        }

        $score = self::get_credit_score($account_uid);
        $anchor_account->credit_score = $score;

        $score = self::get_reputation_score($account_uid);
        $anchor_account->reputation_score = $score;

        $score = self::get_price_score($account_uid);
        $anchor_account->price_score = $score;

        $anchor_account->save();
    }
    //刷新账号
    public static function refreshLivedata($account_uid)
    {
        //获取当前uuid的账户
        $anchor_account = AnchorAccount::where('account_uid', $account_uid)->first();
        //如果账号不存在,则报错
        if (!$anchor_account) {
            throw new \Exception('账号不存在');
        }

        //获取当前uuid的主播
        $anchor = $anchor_account->anchor;

        $livedata_list = self::get_livedata($account_uid);
        foreach ($livedata_list as $livedata_key => $livedata_item) {
            // dump($livedata_item);
            //查询当前直播场次是否存在, 如果存在则更新, 不存在则新增
            $livedata = LiveData::where('anchor_account_id', $anchor_account->id)
                ->where('anchor_id', $anchor->id)
                ->where('start_time', $livedata_item['start_time'])
                ->where('end_time', $livedata_item['end_time'])
                ->first();

            if (!$livedata) {
                $livedata = new LiveData();
                $livedata->anchor_id = $anchor->id;
                $livedata->anchor_account_id = $anchor_account->id;
            }

            $livedata->base_info = $livedata_item['base_info'];
            $livedata->start_time = $livedata_item['start_time'];
            $livedata->end_time = $livedata_item['end_time'];
            $livedata->viewers = $livedata_item['viewers'];
            $livedata->exposure = $livedata_item['exposure'];
            $livedata->clicks = $livedata_item['clicks'];

            $livedata->sales = $livedata_item['sales'];
            $livedata->content_quality = $livedata_item['content_quality'];
            $livedata->save();
        }
    }


    //获取信用分
    public static function get_credit_score($account_uid)
    {
        $url = "https://buyin.jinritemai.com/dashboard/ecology/credit-score";
        $web_code = CookiesService::getWebCodeByUuid($account_uid, $url);
        $doc = new \DOMDocument();
        @$doc->loadHTML($web_code);
        $xpath = new \DOMXPath($doc);
        //<span class="score-value-dpUiWm" elementtiming="element-timing">-</span>
        $xpath_items = $xpath->query('//span[contains(@class, "credit-score-value-score-number-S012fI")]');
        $score = false;
        if ($xpath_items->count() > 0) {
            $score = $xpath_items->item(0)->nodeValue;
            if (!is_numeric($score)) {
                $score = false;
            }
        }
        return $score;
    }


    // 获取口碑分
    public static function get_reputation_score($account_uid)
    {
        $url = "https://buyin.jinritemai.com/dashboard/ecology/reputation-score";
        $web_code=CookiesService::getWebCodeByUuid($account_uid, $url);
        $doc = new \DOMDocument();
        @$doc->loadHTML($web_code);
        $xpath = new \DOMXPath($doc);
        //<span class="score-value-dpUiWm" elementtiming="element-timing">-</span>
        $xpath_items = $xpath->query('//span[contains(@class, "score-value-dpUiWm")]');
        $score = false;
        if ($xpath_items->count() > 0) {
            $score = $xpath_items->item(0)->nodeValue;
            if (!is_numeric($score)) {
                $score = false;
            }
        }
        return $score;
    }

    // 价格分
    public static function get_price_score($account_uid): bool|string
    {
        $url = "https://buyin.jinritemai.com/dashboard/price-diagnose-center";
        $web_code=CookiesService::getWebCodeByUuid($account_uid, $url);
        $doc = new \DOMDocument();
        @$doc->loadHTML($web_code);
        $xpath = new \DOMXPath($doc);
        //<div class="index_module__scoreValue___e5172" elementtiming="element-timing"><div class="index_module__scoreValueInt___e5172" elementtiming="element-timing">71</div><div class="index_module__scoreValueDec___e5172" elementtiming="element-timing">.43</div></div>
        $xpath_items = $xpath->query('//div[contains(@class, "index_module__scoreValue___e5172")]');
        $score = false;
        if ($xpath_items->count() > 0) {
            $score = $xpath_items->item(0)->nodeValue;
            if (!is_numeric($score)) {
                $score = false;
            }
        }
        return $score;
    }

    // 获取直播场次, 数据如下: 直播场次,直播间观看人次,商品曝光次数,商品点击次数,直播间成交金额,内容质量诊断
    public static function get_livedata($account_uid): array
    {
        $url = "https://buyin.jinritemai.com/dashboard/livedata/index";
        $web_code=CookiesService::getWebCodeByUuid($account_uid, $url);
        $doc = new \DOMDocument();
        @$doc->loadHTML($web_code);
        $xpath = new \DOMXPath($doc);

        // 初始化存储所有直播场次的数组
        $livedata = [];

        // XPath查询表格行
        $rows = $xpath->query("//div[contains(@class, 'auxo-table-content')]//tr[contains(@class, 'auxo-table-row')]");

        foreach ($rows as $row) {
            // 直播描述
            $baseInfo = trim($xpath->evaluate("string(.//td[1]//div[@class='baseInfo__title___f152e']/span)", $row));
            // 开始时间
            $startTime = trim($xpath->evaluate('string(.//td[1]//div[contains(@class, "baseInfo__time___f152e") and contains(text(), "开始时间")]/span[@class="baseInfo__detailTime___f152e"])', $row));
            // 结束时间
            $endTime = trim($xpath->evaluate('string(.//td[1]//div[contains(@class, "baseInfo__time___f152e") and contains(text(), "结束时间")]/span[@class="baseInfo__detailTime___f152e"])', $row));
            // 观看次数
            $viewers = trim($xpath->evaluate("string(.//td[2]/span)", $row));
            // 商品曝光次数
            $exposure = trim($xpath->evaluate("string(.//td[3]/span)", $row));
            // 商品点击次数
            $clicks = trim($xpath->evaluate("string(.//td[4]/span)", $row));
            // 成交金额
            $sales = trim($xpath->evaluate("string(.//td[5]/span)", $row));
            // 内容质量
            $contentQuality = trim($xpath->evaluate("string(.//td[6]//span)", $row));

            //去掉¥符号
            $sales = str_replace('¥', '', $sales);

            // 保存到数组
            $livedata[] = [
                'base_info' => $baseInfo,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'viewers' => $viewers,
                'exposure' => $exposure,
                'clicks' => $clicks,
                'sales' => $sales,
                'content_quality' => $contentQuality
            ];
        }
        // 返回JSON格式的数据，或根据需求自定义
        return $livedata;
    }

}
