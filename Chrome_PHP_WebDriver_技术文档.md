# Chrome PHP 和 Selenium WebDriver 技术文档

## 概述

本文档整理了 Chrome PHP 和 Selenium WebDriver 相关的技术资料，为 DYMCN 抖音数据看板系统的数据抓取功能提供技术参考。

## Chrome PHP 库

### 简介

Chrome PHP 是一个 PHP 库，允许从 PHP 控制 Chrome/Chromium 浏览器的无头模式。支持同步和异步操作。

**GitHub 仓库**: https://github.com/chrome-php/chrome  
**Packagist**: https://packagist.org/packages/chrome-php/chrome  
**当前版本**: 1.14.0  
**许可证**: MIT

### 系统要求

- **PHP**: 7.4-8.4
- **Chrome/Chromium**: 65+ 版本
- **操作系统**: Linux (主要测试), macOS, Windows (兼容)

### 安装

```bash
composer require chrome-php/chrome
```

### 基本用法

#### 启动浏览器和创建页面

```php
use HeadlessChromium\BrowserFactory;

$browserFactory = new BrowserFactory();

// 启动无头 Chrome
$browser = $browserFactory->createBrowser();

try {
    // 创建新页面并导航
    $page = $browser->createPage();
    $page->navigate('http://example.com')->waitForNavigation();

    // 获取页面标题
    $pageTitle = $page->evaluate('document.title')->getReturnValue();

    // 截图
    $page->screenshot()->saveToFile('/foo/bar.png');

    // 生成 PDF
    $page->pdf(['printBackground' => false])->saveToFile('/foo/bar.pdf');
} finally {
    // 关闭浏览器
    $browser->close();
}
```

#### 浏览器配置选项

```php
$browserFactory = new BrowserFactory();

$browser = $browserFactory->createBrowser([
    'headless' => false,           // 禁用无头模式（调试用）
    'windowSize' => [1920, 1000],  // 窗口大小
    'enableImages' => false,       // 禁用图片加载
    'userAgent' => 'Custom UA',    // 自定义用户代理
    'noSandbox' => true,          // 无沙盒模式（Docker 环境）
    'debugLogger' => 'php://stdout', // 调试日志
    'connectionDelay' => 0.8,     // 操作间延迟
]);
```

#### 页面操作

**导航和等待**:
```php
// 导航到页面
$navigation = $page->navigate('https://example.com');
$navigation->waitForNavigation();

// 等待特定事件
$navigation->waitForNavigation(Page::DOM_CONTENT_LOADED, 10000);
```

**执行 JavaScript**:
```php
// 执行脚本
$evaluation = $page->evaluate('document.documentElement.innerHTML');
$value = $evaluation->getReturnValue();

// 调用函数
$evaluation = $page->callFunction(
    "function(a, b) { return a + b; }",
    [1, 2]
);
```

**元素操作**:
```php
// 查找元素
$element = $page->dom()->querySelector('#search-input');

// 输入文本
$element->sendKeys('搜索内容');

// 点击元素
$element->click();

// 获取元素属性
$text = $element->getText();
$attr = $element->getAttribute('class');
```

#### 鼠标和键盘操作

**鼠标操作**:
```php
$page->mouse()
    ->move(10, 20)                    // 移动到坐标
    ->click()                         // 左键点击
    ->move(100, 200, ['steps' => 5])  // 分步移动
    ->scrollDown(100);                // 向下滚动
```

**键盘操作**:
```php
$page->keyboard()
    ->typeRawKey('Tab')               // 按键
    ->typeText('输入文本')            // 输入文本
    ->setKeyInterval(10);             // 设置按键间隔
```

#### Cookie 管理

```php
use HeadlessChromium\Cookies\Cookie;

// 设置 Cookie
$page->setCookies([
    Cookie::create('name', 'value', [
        'domain' => 'example.com',
        'expires' => time() + 3600
    ])
])->await();

// 获取 Cookie
$cookies = $page->getCookies();
$cookiesFoo = $cookies->filterBy('name', 'foo');
```

#### 截图和 PDF

**截图**:
```php
$screenshot = $page->screenshot([
    'format' => 'jpeg',           // png, jpeg, webp
    'quality' => 80,              // 质量 (jpeg/webp)
    'optimizeForSpeed' => true,   // 优化速度
    'captureBeyondViewport' => true, // 全页截图
    'clip' => new Clip(10, 10, 100, 100) // 截图区域
]);

$screenshot->saveToFile('/path/to/screenshot.jpg');
```

**PDF 生成**:
```php
$pdf = $page->pdf([
    'landscape' => true,
    'printBackground' => true,
    'marginTop' => 0.0,
    'marginBottom' => 1.4,
    'paperWidth' => 8.5,
    'paperHeight' => 11.0,
    'scale' => 1.0
]);

$pdf->saveToFile('/path/to/file.pdf');
```

### 高级功能

#### 持久化浏览器

```php
// 保存浏览器连接
$browser = $factory->createBrowser(['keepAlive' => true]);
file_put_contents('/tmp/chrome-socket', $browser->getSocketUri());

// 重新连接
$socket = file_get_contents('/tmp/chrome-socket');
try {
    $browser = BrowserFactory::connectToBrowser($socket);
} catch (BrowserConnectionFailed $e) {
    // 重新启动浏览器
}
```

#### 直接 DevTools 协议通信

```php
use HeadlessChromium\Communication\Connection;
use HeadlessChromium\Communication\Message;

$connection = new Connection($webSocketUri);
$connection->connect();

// 发送消息
$responseReader = $connection->sendMessage(
    new Message('Target.activateTarget', ['targetId' => 'xxx'])
);

$response = $responseReader->waitForResponse(1000);
```

## Selenium WebDriver PHP

### 简介

PHP WebDriver 是 Selenium WebDriver 的 PHP 语言绑定，允许从 PHP 控制 Web 浏览器。

**GitHub 仓库**: https://github.com/php-webdriver/php-webdriver  
**Packagist**: https://packagist.org/packages/php-webdriver/webdriver  
**许可证**: MIT

### 安装

```bash
composer require php-webdriver/webdriver
```

### 基本用法

#### 启动浏览器会话

```php
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\DesiredCapabilities;

// 服务器 URL
$serverUrl = 'http://localhost:4444'; // ChromeDriver/GeckoDriver
// $serverUrl = 'http://localhost:4444/wd/hub'; // Selenium 2.x/3.x

// 创建浏览器会话
$driver = RemoteWebDriver::create($serverUrl, DesiredCapabilities::chrome());
```

#### 自定义浏览器能力

```php
use Facebook\WebDriver\Chrome\ChromeOptions;
use Facebook\WebDriver\Remote\DesiredCapabilities;

$desiredCapabilities = DesiredCapabilities::chrome();

// 设置基本能力
$desiredCapabilities->setCapability('acceptSslCerts', false);

// Chrome 选项
$chromeOptions = new ChromeOptions();
$chromeOptions->addArguments(['--headless', '--no-sandbox']);
$desiredCapabilities->setCapability(ChromeOptions::CAPABILITY, $chromeOptions);

$driver = RemoteWebDriver::create($serverUrl, $desiredCapabilities);
```

#### 页面操作

```php
use Facebook\WebDriver\WebDriverBy;

// 导航
$driver->get('https://example.com');

// 查找元素
$element = $driver->findElement(WebDriverBy::id('search-input'));
$elements = $driver->findElements(WebDriverBy::className('item'));

// 元素操作
$element->sendKeys('搜索内容');
$element->submit();
$element->click();

// 获取信息
$text = $element->getText();
$attribute = $element->getAttribute('class');
$title = $driver->getTitle();
```

#### 等待机制

```php
use Facebook\WebDriver\WebDriverWait;
use Facebook\WebDriver\WebDriverExpectedCondition;

$wait = new WebDriverWait($driver, 10); // 等待最多 10 秒

// 等待元素可见
$wait->until(
    WebDriverExpectedCondition::visibilityOfElementLocated(
        WebDriverBy::id('dynamic-element')
    )
);

// 等待元素可点击
$wait->until(
    WebDriverExpectedCondition::elementToBeClickable(
        WebDriverBy::id('submit-button')
    )
);
```

#### 窗口和框架操作

```php
// 窗口操作
$driver->manage()->window()->maximize();
$driver->manage()->window()->setSize(new WebDriverDimension(1024, 768));

// 切换窗口
$handles = $driver->getWindowHandles();
$driver->switchTo()->window($handles[1]);

// 切换框架
$driver->switchTo()->frame('frame-name');
$driver->switchTo()->defaultContent(); // 回到主文档
```

### 测试框架集成

#### PHPUnit 集成示例

```php
use PHPUnit\Framework\TestCase;
use Facebook\WebDriver\Remote\RemoteWebDriver;
use Facebook\WebDriver\Remote\DesiredCapabilities;

class WebTest extends TestCase
{
    protected $webDriver;

    protected function setUp(): void
    {
        $capabilities = DesiredCapabilities::chrome();
        $this->webDriver = RemoteWebDriver::create(
            'http://localhost:4444',
            $capabilities
        );
    }

    protected function tearDown(): void
    {
        $this->webDriver->quit();
    }

    public function testGoogleSearch()
    {
        $this->webDriver->get('https://www.google.com');
        
        $searchBox = $this->webDriver->findElement(
            WebDriverBy::name('q')
        );
        $searchBox->sendKeys('Selenium PHP');
        $searchBox->submit();

        $this->assertStringContainsString(
            'Selenium PHP',
            $this->webDriver->getTitle()
        );
    }
}
```

#### 并行测试 (ParaTest)

```bash
# 安装 ParaTest
composer require brianium/paratest

# 运行并行测试
vendor/bin/paratest --processes=4 --functional test
```

### 云端测试服务

#### LambdaTest 集成

```php
$capabilities = [
    "build" => "PHP Selenium Test",
    "name" => "Test on Chrome",
    "platform" => "Windows 10",
    "browserName" => "Chrome",
    "version" => "latest"
];

$username = "your-username";
$accessKey = "your-access-key";
$gridUrl = "https://{$username}:{$accessKey}@hub.lambdatest.com/wd/hub";

$driver = RemoteWebDriver::create($gridUrl, $capabilities);
```

## 在 DYMCN 项目中的应用

### 数据抓取架构

```php
// 服务类示例
class DouyinDataService
{
    private $browser;
    private $page;

    public function __construct()
    {
        $factory = new BrowserFactory();
        $this->browser = $factory->createBrowser([
            'headless' => true,
            'userAgent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)...'
        ]);
    }

    public function loginWithCookies($cookies)
    {
        $this->page = $this->browser->createPage();
        $this->page->setCookies($cookies)->await();
        $this->page->navigate('https://creator.douyin.com')->waitForNavigation();
    }

    public function getLiveData($roomId)
    {
        $this->page->navigate("https://creator.douyin.com/live/{$roomId}");
        $this->page->waitForNavigation();

        // 提取数据
        $data = $this->page->evaluate('
            return {
                viewers: document.querySelector(".viewer-count").textContent,
                sales: document.querySelector(".sales-amount").textContent,
                // ... 其他数据
            };
        ')->getReturnValue();

        return $data;
    }

    public function __destruct()
    {
        if ($this->browser) {
            $this->browser->close();
        }
    }
}
```

### Cookie 管理集成

```php
// 与现有 Cookie 模型集成
class CookieManager
{
    public function applyCookiesToPage($page, $cookieModel)
    {
        $cookies = json_decode($cookieModel->cookies, true);
        $chromeCookies = [];

        foreach ($cookies as $cookie) {
            $chromeCookies[] = Cookie::create(
                $cookie['name'],
                $cookie['value'],
                [
                    'domain' => $cookie['domain'],
                    'path' => $cookie['path'] ?? '/',
                    'expires' => $cookie['expires'] ?? null
                ]
            );
        }

        $page->setCookies($chromeCookies)->await();
    }
}
```

### 队列任务集成

```php
// Laravel 队列任务
class CheckLiveDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        $service = new DouyinDataService();
        
        // 获取活跃的 Cookie
        $cookies = Cookies::where('is_online', 1)->get();
        
        foreach ($cookies as $cookie) {
            try {
                $service->loginWithCookies($cookie->cookies);
                // 抓取数据逻辑
            } catch (Exception $e) {
                Log::error('数据抓取失败', ['error' => $e->getMessage()]);
            }
        }
    }
}
```

## 最佳实践

### 1. 错误处理

```php
try {
    $page->navigate($url)->waitForNavigation();
} catch (OperationTimedOut $e) {
    // 页面加载超时
    Log::warning('页面加载超时', ['url' => $url]);
} catch (NavigationExpired $e) {
    // 页面已跳转到其他地址
    Log::info('页面发生跳转');
}
```

### 2. 资源管理

```php
// 确保浏览器正确关闭
register_shutdown_function(function() use ($browser) {
    if ($browser) {
        $browser->close();
    }
});
```

### 3. 性能优化

```php
// 禁用不必要的资源
$browser = $factory->createBrowser([
    'enableImages' => false,        // 禁用图片
    'disableNotifications' => true, // 禁用通知
    'customFlags' => [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
    ]
]);
```

### 4. 反检测措施

```php
// 模拟真实用户行为
$page->addPreScript('
    Object.defineProperty(navigator, "webdriver", {
        get: () => undefined,
    });
');

// 随机延迟
$page->mouse()->move(100, 100, ['steps' => rand(3, 8)]);
sleep(rand(1, 3));
```

## 故障排除

### 常见问题

1. **Chrome 启动失败**
   - 检查 Chrome 路径
   - 确保有足够权限
   - 在 Docker 中使用 `--no-sandbox`

2. **页面加载超时**
   - 增加等待时间
   - 检查网络连接
   - 使用不同的等待事件

3. **元素找不到**
   - 确认页面已完全加载
   - 使用显式等待
   - 检查元素选择器

4. **Cookie 失效**
   - 定期更新 Cookie
   - 检查域名匹配
   - 验证 Cookie 格式

### 调试技巧

```php
// 启用调试模式
$browser = $factory->createBrowser([
    'headless' => false,
    'debugLogger' => 'php://stdout',
    'connectionDelay' => 1.0
]);

// 截图调试
$page->screenshot()->saveToFile('/tmp/debug.png');

// 获取页面源码
$html = $page->getHtml();
file_put_contents('/tmp/page.html', $html);
```

## 参考资源

- [Chrome PHP GitHub](https://github.com/chrome-php/chrome)
- [PHP WebDriver GitHub](https://github.com/php-webdriver/php-webdriver)
- [Selenium 官方文档](https://selenium.dev/documentation/)
- [Chrome DevTools Protocol](https://chromedevtools.github.io/devtools-protocol/)
- [LambdaTest PHP 教程](https://www.lambdatest.com/blog/selenium-php-tutorial/)

---

*本文档将根据项目需求和技术发展持续更新。*
