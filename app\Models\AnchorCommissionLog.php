<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AnchorCommissionLog extends Model
{
    use HasFactory;

    protected $table = 'dymcn_anchor_commission_logs';

    protected $fillable = [
        'anchor_id',
        'anchor_account_id',
        'livedata_id',
        'commission_id',
        'change_type',
        'change_amount',
        'operator',
        'remark',
    ];

    /**
     * 获取变动类型列表
     */
    public static function getChangeTypeList(): array
    {
        return [
            'add_commission' => '添加佣金',
            'refund_commission' => '退回佣金',
        ];
    }

    /**
     * 关联主播
     */
    public function anchor(): BelongsTo
    {
        return $this->belongsTo(Anchor::class);
    }

    /**
     * 关联主播账号
     */
    public function anchorAccount(): BelongsTo
    {
        return $this->belongsTo(AnchorAccount::class);
    }

    /**
     * 关联直播场次
     */
    public function liveData(): BelongsTo
    {
        return $this->belongsTo(LiveData::class, 'livedata_id');
    }

    /**
     * 关联佣金审核记录
     */
    public function commission(): BelongsTo
    {
        return $this->belongsTo(LiveDataCommission::class, 'commission_id');
    }
}
