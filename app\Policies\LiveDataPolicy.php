<?php

namespace App\Policies;

use App\Models\User;
use App\Models\LiveData;
use Illuminate\Auth\Access\HandlesAuthorization;

class LiveDataPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_live::data');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, LiveData $liveData): bool
    {
        return $user->can('view_live::data');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_live::data');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, LiveData $liveData): bool
    {
        return $user->can('update_live::data');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, LiveData $liveData): bool
    {
        return $user->can('delete_live::data');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_live::data');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, LiveData $liveData): bool
    {
        return $user->can('force_delete_live::data');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_live::data');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, LiveData $liveData): bool
    {
        return $user->can('restore_live::data');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_live::data');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, LiveData $liveData): bool
    {
        return $user->can('replicate_live::data');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_live::data');
    }



    //是否可以查询任意用户的数据
    public function view_any_user_data(User $user): bool
    {
        return $user->can('view_any_user_data_live::data');
    }


    //是否可查看直播场次佣金
    public function view_live_data_commission(User $user): bool
    {
        return $user->can('view_live_data_commission_live::data');
    }
    //是否可提交添加佣金
    public function add_live_data_commission(User $user): bool
    {
        return $user->can('add_live_data_commission_live::data');
    }
    //是否可提交退佣金
    public function refund_live_data_commission(User $user): bool
    {
        return $user->can('refund_live_data_commission_live::data');
    }
    //是否可刷新罗盘数据
    public function refresh_live_data_luopan_detail(User $user): bool
    {
        return $user->can('refresh_live_data_luopan_detail_live::data');
    }
}
