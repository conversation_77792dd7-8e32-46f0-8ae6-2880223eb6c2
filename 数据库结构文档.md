# DYMCN 抖音数据看板系统 - 数据库结构文档

## 数据库概述

DYMCN 系统采用 MySQL 数据库，主要用于管理抖音 MCN 业务数据，包括主播管理、直播数据统计、佣金管理等核心功能。数据库设计遵循 Laravel 框架规范，支持完整的业务流程管理。

## 核心业务表

### 1. 用户表 (users)

**表名**: `users`  
**用途**: 系统用户管理，包括管理员和助理账号

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| id | bigint | - | - | NO | 主键ID |
| name | varchar | 255 | - | NO | 用户名 |
| email | varchar | 255 | - | NO | 邮箱地址(唯一) |
| email_verified_at | timestamp | - | NULL | YES | 邮箱验证时间 |
| password | varchar | 255 | - | NO | 密码哈希 |
| avatar_url | varchar | 255 | NULL | YES | 头像URL |
| remember_token | varchar | 100 | NULL | YES | 记住登录令牌 |
| created_at | timestamp | - | NULL | YES | 创建时间 |
| updated_at | timestamp | - | NULL | YES | 更新时间 |

**索引**:
- PRIMARY KEY (`id`)
- UNIQUE KEY (`email`)

### 2. 主播表 (dymcn_anchor)

**表名**: `dymcn_anchor`  
**用途**: 主播基本信息管理

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| id | bigint | - | - | NO | 主键ID |
| user_id | int | - | 0 | NO | 所属用户ID |
| real_name | varchar | 255 | - | NO | 主播姓名 |
| avatar | varchar | 255 | '' | NO | 小头像 |
| avatar_big | varchar | 255 | '' | NO | 大头像 |
| phone | varchar | 255 | - | NO | 电话号码 |
| id_card | varchar | 255 | - | NO | 身份证号码 |
| total_commission | decimal | 10,2 | 0.00 | NO | 累计佣金 |
| returned_commission | decimal | 10,2 | 0.00 | NO | 累计已退佣 |
| real_commission | decimal | 10,2 | 0.00 | NO | 实际佣金 |
| payment_commission | decimal | 10,2 | 0.00 | NO | 累计已结算 |
| remark | text | - | NULL | YES | 备注信息 |
| created_at | timestamp | - | NULL | YES | 创建时间 |
| updated_at | timestamp | - | NULL | YES | 更新时间 |

**关系**:
- 属于用户 (belongsTo User)
- 拥有多个账号 (hasMany AnchorAccount)
- 拥有多个佣金记录 (hasMany AnchorCommissionLog)

### 3. 主播账号表 (dymcn_anchor_account)

**表名**: `dymcn_anchor_account`  
**用途**: 主播抖音账号信息管理

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| id | bigint | - | - | NO | 主键ID |
| anchor_id | int | - | 0 | NO | 所属主播ID |
| account_uid | varchar | 255 | '' | NO | 账号UID |
| account_name | varchar | 255 | '' | NO | 账号名 |
| account_status | varchar | 255 | '' | NO | 账号状态 |
| real_name | varchar | 255 | '' | NO | 真实姓名 |
| phone | varchar | 255 | '' | NO | 电话号码 |
| id_card_no | varchar | 255 | '' | NO | 身份证号码 |
| device | varchar | 255 | '' | NO | 账号设备 |
| certificate_no | varchar | 255 | '' | NO | 执照编号 |
| certificate_legal_person | varchar | 255 | '' | NO | 执照法人 |
| certificate_image | varchar | 255 | '' | NO | 执照图 |
| margin_amount | decimal | 10,2 | 0.00 | NO | 保证金 |
| reputation_score | decimal | 10,2 | 0.00 | NO | 口碑分 |
| credit_score | decimal | 10,2 | 0.00 | NO | 信用分 |
| price_score | decimal | 10,2 | 0.00 | NO | 价格分 |
| remark | text | - | NULL | YES | 备注信息 |
| created_at | timestamp | - | NULL | YES | 创建时间 |
| updated_at | timestamp | - | NULL | YES | 更新时间 |

**关系**:
- 属于主播 (belongsTo Anchor)
- 拥有多个直播数据 (hasMany LiveData)

### 4. 直播数据表 (dymcn_livedata)

**表名**: `dymcn_livedata`  
**用途**: 直播场次数据统计

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| id | bigint | - | - | NO | 主键ID |
| anchor_id | int | - | 0 | NO | 所属主播ID |
| anchor_account_id | int | - | 0 | NO | 所属账号ID |
| room_id | varchar | 255 | '' | NO | 场次ID |
| base_info | varchar | 255 | '' | NO | 直播标题 |
| start_time | datetime | - | - | NO | 开始时间 |
| end_time | datetime | - | - | NO | 结束时间 |
| duration_second | bigint | - | 0 | NO | 持续秒数 |
| viewers | int | - | 0 | NO | 观看人数 |
| exposure | int | - | 0 | NO | 商品曝光次数 |
| clicks | int | - | 0 | NO | 商品点击次数 |
| sales | decimal | 10,2 | 0.00 | NO | 成交金额 |
| content_quality | varchar | 255 | '' | NO | 内容质量 |
| total_commission | decimal | 10,2 | 0.00 | NO | 累计佣金 |
| returned_commission | decimal | 10,2 | 0.00 | NO | 累计已退佣 |
| real_commission | decimal | 10,2 | 0.00 | NO | 实际佣金 |
| payment_commission | decimal | 10,2 | 0.00 | NO | 累计已结算 |
| is_completed_lock | int | - | 0 | YES | 是否完成锁定 |
| remark | text | - | NULL | YES | 备注信息 |

**扩展统计字段**:
| 字段名 | 类型 | 长度 | 默认值 | 注释 |
|--------|------|------|--------|------|
| avg_watch_duration | bigint | - | 0 | 平均观看时长(秒) |
| fans_club_ucnt | int | - | 0 | 新增直播团人数 |
| gmv | bigint | - | 0 | GMV(分) |
| gpm | bigint | - | 0 | GPM(分) |
| incr_fans_cnt | int | - | 0 | 新增粉丝数 |
| online_user_cnt | int | - | 0 | 平均在线人数 |
| online_user_ucnt | int | - | 0 | 累计观看人数 |
| pay_cnt | int | - | 0 | 成交件数 |
| pay_fans_ratio | decimal | 10,4 | 0.0000 | 成交粉丝占比 |
| pay_ucnt | int | - | 0 | 成交人数 |
| product_click_to_pay_rate | decimal | 10,4 | 0.0000 | 点击-成交转化率 |
| flow_order_source | json | - | NULL | 流量分析数据 |
| created_at | timestamp | - | NULL | 创建时间 |
| updated_at | timestamp | - | NULL | 更新时间 |

**关系**:
- 属于主播 (belongsTo Anchor)
- 属于主播账号 (belongsTo AnchorAccount)
- 拥有多个佣金记录 (hasMany LiveDataCommission)

### 5. 直播佣金表 (dymcn_livedata_commission)

**表名**: `dymcn_livedata_commission`  
**用途**: 直播佣金申请和审批管理

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| id | bigint | - | - | NO | 主键ID |
| livedata_id | int | - | 0 | NO | 所属场次ID |
| req_type | varchar | 255 | '' | NO | 申请类型 |
| req_username | varchar | 255 | '' | NO | 申请人 |
| req_amount | decimal | 10,2 | 0.00 | NO | 申请金额 |
| req_remark | text | - | NULL | YES | 申请备注 |
| req_images | text | - | NULL | YES | 申请图片(JSON) |
| approval_status | varchar | 255 | '' | YES | 审批状态 |
| approver_name | varchar | 255 | '' | YES | 审批操作人 |
| approval_remark | text | - | NULL | YES | 审批意见 |
| created_at | timestamp | - | NULL | YES | 创建时间 |
| updated_at | timestamp | - | NULL | YES | 更新时间 |

**申请类型枚举**:
- `add_commission`: 添加佣金
- `refund_commission`: 佣金退佣

**审批状态枚举**:
- `pending`: 待审核
- `approved`: 已通过
- `rejected`: 已拒绝

**关系**:
- 属于直播场次 (belongsTo LiveData)

### 6. 主播佣金变动记录表 (dymcn_anchor_commission_logs)

**表名**: `dymcn_anchor_commission_logs`  
**用途**: 记录主播佣金的所有变动情况，用于统计分析

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| id | bigint | - | - | NO | 主键ID |
| anchor_id | int | - | 0 | NO | 所属主播ID |
| anchor_account_id | int | - | 0 | NO | 所属主播账号ID |
| livedata_id | int | - | 0 | NO | 关联直播场次ID |
| commission_id | int | - | 0 | NO | 关联佣金审核记录ID |
| change_type | varchar | 255 | - | NO | 变动类型 |
| change_amount | decimal | 10,2 | 0.00 | NO | 变动金额 |
| operator | varchar | 255 | '' | NO | 操作人 |
| remark | text | - | NULL | YES | 备注信息 |
| created_at | timestamp | - | NULL | YES | 创建时间 |
| updated_at | timestamp | - | NULL | YES | 更新时间 |

**变动类型枚举**:
- `add_commission`: 添加佣金
- `refund_commission`: 退回佣金

**索引**:
- KEY (`anchor_id`)
- KEY (`anchor_account_id`)
- KEY (`livedata_id`)
- KEY (`commission_id`)
- KEY (`created_at`)

**关系**:
- 属于主播 (belongsTo Anchor)
- 属于主播账号 (belongsTo AnchorAccount)
- 属于直播场次 (belongsTo LiveData)
- 属于佣金审核记录 (belongsTo LiveDataCommission)

### 7. Cookie管理表 (dymcn_cookies)

**表名**: `dymcn_cookies`  
**用途**: 管理抖音登录Cookie，用于数据抓取

| 字段名 | 类型 | 长度 | 默认值 | 是否为空 | 注释 |
|--------|------|------|--------|----------|------|
| id | bigint | - | - | NO | 主键ID |
| uuid | varchar | 255 | '' | NO | 唯一标识 |
| taburl | varchar | 255 | '' | NO | 页面URL |
| domain | varchar | 255 | '' | NO | 域名 |
| useragent | varchar | 255 | '' | NO | 用户代理 |
| cookies | longtext | - | - | NO | Cookie数据 |
| local_storage | longtext | - | NULL | YES | 本地存储数据 |
| is_online | tinyint | - | 0 | NO | 在线状态(-1=不在线,0=未知,1=在线) |
| keeptime | bigint | - | - | NO | 保活时间 |
| remark | text | - | NULL | YES | 备注信息 |
| created_at | timestamp | - | NULL | YES | 创建时间 |
| updated_at | timestamp | - | NULL | YES | 更新时间 |

## 系统管理表

### 8. 权限管理表组

**权限表 (permissions)**:
| 字段名 | 类型 | 注释 |
|--------|------|------|
| id | bigint | 主键ID |
| name | varchar(255) | 权限名称 |
| guard_name | varchar(255) | 守卫名称 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

**角色表 (roles)**:
| 字段名 | 类型 | 注释 |
|--------|------|------|
| id | bigint | 主键ID |
| name | varchar(255) | 角色名称 |
| guard_name | varchar(255) | 守卫名称 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

**用户角色关联表 (model_has_roles)**:
- 多对多关系表，关联用户和角色

**角色权限关联表 (role_has_permissions)**:
- 多对多关系表，关联角色和权限

### 9. 系统设置表 (settings)

**表名**: `settings`  
**用途**: 系统配置参数管理

| 字段名 | 类型 | 注释 |
|--------|------|------|
| id | bigint | 主键ID |
| group | varchar(255) | 配置组 |
| name | varchar(255) | 配置名称 |
| locked | boolean | 是否锁定 |
| payload | json | 配置数据 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

**索引**:
- UNIQUE KEY (`group`, `name`)

### 10. 活动日志表 (activity_log)

**表名**: `activity_log`  
**用途**: 记录系统操作日志

| 字段名 | 类型 | 注释 |
|--------|------|------|
| id | bigint | 主键ID |
| log_name | varchar(255) | 日志名称 |
| description | text | 操作描述 |
| subject_type | varchar(255) | 操作对象类型 |
| subject_id | bigint | 操作对象ID |
| causer_type | varchar(255) | 操作者类型 |
| causer_id | bigint | 操作者ID |
| properties | json | 操作属性 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

## 辅助功能表

### 11. 通知表 (notifications)

**表名**: `notifications`  
**用途**: 系统通知管理

| 字段名 | 类型 | 注释 |
|--------|------|------|
| id | uuid | 主键ID |
| type | varchar(255) | 通知类型 |
| notifiable_type | varchar(255) | 通知对象类型 |
| notifiable_id | bigint | 通知对象ID |
| data | text | 通知数据 |
| read_at | timestamp | 阅读时间 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

### 12. 导入导出表

**导入表 (imports)**:
| 字段名 | 类型 | 注释 |
|--------|------|------|
| id | bigint | 主键ID |
| completed_at | timestamp | 完成时间 |
| file_name | varchar(255) | 文件名 |
| file_path | varchar(255) | 文件路径 |
| importer | varchar(255) | 导入器 |
| processed_rows | int | 已处理行数 |
| total_rows | int | 总行数 |
| successful_rows | int | 成功行数 |
| user_id | bigint | 用户ID |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

**导出表 (exports)**:
| 字段名 | 类型 | 注释 |
|--------|------|------|
| id | bigint | 主键ID |
| completed_at | timestamp | 完成时间 |
| file_disk | varchar(255) | 文件磁盘 |
| file_name | varchar(255) | 文件名 |
| exporter | varchar(255) | 导出器 |
| processed_rows | int | 已处理行数 |
| total_rows | int | 总行数 |
| successful_rows | int | 成功行数 |
| user_id | bigint | 用户ID |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

## 数据关系图

```
users (用户)
├── anchors (主播) [1:N]
    ├── anchor_accounts (主播账号) [1:N]
    │   └── live_data (直播数据) [1:N]
    │       └── live_data_commissions (直播佣金) [1:N]
    └── anchor_commission_logs (佣金变动记录) [1:N]

cookies (Cookie管理) [独立表]

permissions (权限) [N:N] roles (角色) [N:N] users (用户)
settings (系统设置) [独立表]
activity_log (操作日志) [多态关联]
notifications (通知) [多态关联]
imports/exports (导入导出) [关联用户]
```

## 数据统计逻辑

### 佣金统计机制

1. **主播佣金统计**: 通过 `AnchorCommissionStat` 模型（实际使用 `dymcn_anchor` 表）
2. **变动记录**: 所有佣金变动都记录在 `dymcn_anchor_commission_logs` 表
3. **统计查询**: 基于时间范围查询佣金变动记录进行统计
4. **实时计算**: 统计数据通过查询变动记录实时计算，不存储冗余数据

### 权限控制机制

1. **数据隔离**: 普通用户只能查看自己管理的主播数据
2. **超级管理员**: 可以查看所有数据
3. **权限粒度**: 支持资源级别的细粒度权限控制
4. **特殊权限**: 如 `view_any_user_data` 允许查看任意用户数据

## 性能优化建议

### 索引优化
1. 在 `dymcn_anchor_commission_logs` 表的关联字段和时间字段上建立索引
2. 在 `dymcn_livedata` 表的查询字段上建立复合索引
3. 在 `dymcn_cookies` 表的 `uuid` 和 `domain` 字段上建立索引

### 查询优化
1. 佣金统计查询使用时间范围限制
2. 大数据量查询使用分页和缓存
3. 复杂统计查询考虑使用数据库视图或物化视图

### 数据归档
1. 定期归档历史直播数据
2. 清理过期的Cookie数据
3. 压缩历史操作日志

---

*本文档基于当前数据库迁移文件生成，如有数据库结构变更请及时更新文档。*
