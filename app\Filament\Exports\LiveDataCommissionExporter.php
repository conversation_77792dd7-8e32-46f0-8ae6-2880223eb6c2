<?php

namespace App\Filament\Exports;

use App\Models\LiveDataCommission;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;

class LiveDataCommissionExporter extends Exporter
{
    protected static ?string $model = LiveDataCommission::class;

    public static function getNotifyUserOfCompletedExport(): bool
    {
        return true;
    }

    public function getFileDisk(): string
    {
        return 'local'; // 使用本地磁盘存储
    }

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('id')
                ->label('ID'),
            ExportColumn::make('req_username')
                ->label('申请人'),
            ExportColumn::make('req_type')
                ->label('佣金类型')
                ->formatStateUsing(function ($state) {
                    $reqTypeList = LiveDataCommission::getReqTypeList();
                    return $reqTypeList[$state] ?? '不支持的类型';
                }),
            ExportColumn::make('req_amount')
                ->label('佣金金额'),
            ExportColumn::make('approval_status')
                ->label('审批状态')
                ->formatStateUsing(function ($state) {
                    $statusList = LiveDataCommission::getApprovalStatusList();
                    return $statusList[$state] ?? '未知状态';
                }),
            ExportColumn::make('approver_name')
                ->label('审批人'),
            ExportColumn::make('livedata.base_info')
                ->label('直播场次'),
            ExportColumn::make('req_remark')
                ->label('备注'),
            ExportColumn::make('created_at')
                ->label('创建时间'),
            ExportColumn::make('updated_at')
                ->label('更新时间'),
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        return '直播佣金数据已导出完成.';
    }

    public function getFileName(Export $export): string
    {
        return '直播佣金数据导出_' . now()->format('Y_m_d_His');
    }

    public function getBuilder(): Builder
    {
        // 这里可以添加额外的查询条件
        return static::getModel()::query();
    }
}
