<?php

namespace App\Filament\Exports;

use App\Models\LiveData;
use Filament\Actions\Exports\Models\Export;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Illuminate\Database\Eloquent\Builder;

class LiveDataExporter extends Exporter
{
    protected static ?string $model = LiveData::class;

    public static function getNotifyUserOfCompletedExport(): bool
    {
        return true;
    }

    public function getFileDisk(): string
    {
        return 'local'; // 使用本地磁盘存储
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        return '直播数据已成功导出。';
    }

    public function getFileName(Export $export): string
    {
        return '直播数据导出_' . now()->format('Y_m_d_His');
    }

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('base_info')
                ->label('直播标题'),
            ExportColumn::make('start_time')
                ->label('开始时间'),
            ExportColumn::make('end_time')
                ->label('结束时间'),
            ExportColumn::make('total_commission')
                ->label('累计佣金'),
            ExportColumn::make('returned_commission')
                ->label('已退佣金'),
            ExportColumn::make('real_commission')
                ->label('实际佣金'),
            ExportColumn::make('sales')
                ->label('商品成交额'),
            ExportColumn::make('viewers')
                ->label('观看次数'),
            ExportColumn::make('exposure')
                ->label('商品曝光次数'),
            ExportColumn::make('clicks')
                ->label('商品点击次数'),
            ExportColumn::make('content_quality')
                ->label('内容质量'),
            ExportColumn::make('avg_watch_duration')
                ->label('平均观看时长(秒)'),
            ExportColumn::make('fans_club_ucnt')
                ->label('新增直播团人数'),
            ExportColumn::make('gmv')
                ->label('客单价(分)'),
            ExportColumn::make('gpm')
                ->label('客单价(分)'),
            ExportColumn::make('incr_fans_cnt')
                ->label('新增粉丝数'),
            ExportColumn::make('online_user_cnt')
                ->label('平均在线人数'),
            ExportColumn::make('online_user_ucnt')
                ->label('累计在线人数'),
            ExportColumn::make('pay_cnt')
                ->label('成交件数'),
            ExportColumn::make('pay_fans_ratio')
                ->label('成交粉丝占比'),
            ExportColumn::make('pay_ucnt')
                ->label('成交人数'),
            ExportColumn::make('product_click_to_pay_rate')
                ->label('点击-成交转化率'),
            ExportColumn::make('flow_order_source')
                ->label('流量分析数据'),
            ExportColumn::make('created_at')
                ->label('创建时间'),
            ExportColumn::make('updated_at')
                ->label('更新时间'),
        ];
    }
}
