<?php

namespace App\Models;


// use Illuminate\Contracts\Auth\MustVerifyEmail;
use BezhanSalleh\FilamentShield\Support\Utils;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPanelShield;
use Filament\Models\Contracts\FilamentUser;
use Filament\Panel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

//灯丝权限盾
use Spatie\Permission\Traits\HasRoles;

//灯丝头像
use Illuminate\Support\Facades\Storage;
use Filament\Models\Contracts\HasAvatar;
use Illuminate\Auth\Access\HandlesAuthorization;


class User extends Authenticatable implements FilamentUser, HasAvatar
{

    use HasFactory, Notifiable;

    //灯丝权限盾
    use HasRoles;
    use HasPanelShield;

    //深度的关系
    use \Staudenmeir\EloquentHasManyDeep\HasRelationships;
    use HandlesAuthorization;


    public function canAccessPanel(Panel $panel): bool
    {
        // 输出当前用户的角色
        // dump($this->getRoleNames()->toArray());
        return $this->hasRole(Utils::getSuperAdminName()) || $this->hasRole("财务") || $this->hasRole("助理") || $this->hasRole("运营");
    }

    public function isSuperAdmin(): bool
    {
        return $this->hasRole(Utils::getSuperAdminName());
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar_url',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password'          => 'hashed',
        ];
    }

    //是否可以模拟用户
    public function canImpersonate()
    {
        return true;
    }

    //允许模拟那些用户
    public function canBeImpersonated()
    {
        return true;
    }


    //灯丝头像
    public function getFilamentAvatarUrl(): ?string
    {
        return $this->avatar_url ? Storage::url($this->avatar_url) : null;
    }


    //获取用户下的主播
    public function anchors()
    {
        return $this->hasMany(Anchor::class);
    }

    //获取用户下的主播账号
    public function anchorAccounts()
    {
        return $this->hasManyThrough(AnchorAccount::class, Anchor::class, 'user_id', 'anchor_id', 'id', 'id');
    }


    public function cookies(): \Staudenmeir\EloquentHasManyDeep\HasManyDeep
    {
        return $this->hasManyDeep(Cookies::class, [Anchor::class, AnchorAccount::class],
            ['user_id', 'anchor_id', 'uuid'],
            ['id', 'id', 'account_uid']
        );
    }


    public function liveDatas(): \Staudenmeir\EloquentHasManyDeep\HasManyDeep
    {
        return $this->hasManyDeep(LiveData::class, [Anchor::class, AnchorAccount::class],
            ['user_id', 'anchor_id', 'anchor_account_id'],
            ['id', 'id', 'id']
        );
    }

    public function liveDataCommissions(): \Staudenmeir\EloquentHasManyDeep\HasManyDeep
    {
        return $this->hasManyDeep(LiveDataCommission::class, [Anchor::class, AnchorAccount::class, LiveData::class],
            ['user_id', 'anchor_id', 'anchor_account_id', 'livedata_id'],
            ['id', 'id', 'id', 'id']
        );
    }



    // //获取用户下的主播的主播账号的所有cookies
    // public function cookies(): \Staudenmeir\EloquentHasManyDeep\HasManyDeep
    // {
    //     return $this->hasManyDeepFromRelations($this->anchors(), (new AnchorAccount())->cookies());
    // }


    public function getFilamentName()
    {
        return $this->name;
    }

    /**
     * 检查用户是否有指定权限
     */
    public function can($abilities, $arguments = []): bool
    {
        if (empty($abilities)) {
            return false;
        }

        // 如果是超级管理员, 默认就是有权限
        if ($this->isSuperAdmin()) {
            return true;
        }

        return parent::can($abilities, $arguments);
    }
}
