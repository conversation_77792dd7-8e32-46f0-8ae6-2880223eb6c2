<?php

namespace App\Filament\Resources\AnchorAccountResource\Pages;

use App\Filament\Resources\AnchorAccountResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListAnchorAccounts extends ListRecords
{
    protected static string $resource = AnchorAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }



    public function getTabs(): array
    {
        return [
            'my_all' => Tab::make('属于我管理的主播账号')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNot('anchor_id', '0')),
            'all' => Tab::make('我能管理的全部账号'),
            'anchor_id_eq_0' => Tab::make('未归属主播账号')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('anchor_id', '0')),
            'account_status_not_ok' => Tab::make('非正常状态账号')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNot('account_status', '正常')),
            'cookies_online' => Tab::make('Cookies在线账号')
                ->modifyQueryUsing(function (Builder $query) {
                    return $query->whereHas('cookies', function ($q) {
                        $q->where('is_online', 1);
                    });
                }),
            'cookies_offline' => Tab::make('Cookies不在线账号')
                ->modifyQueryUsing(function (Builder $query) {
                    return $query->whereDoesntHave('cookies', function ($q) {
                        $q->where('is_online', 1);
                    });
                }),
        ];
    }

    /**
     * 设置默认激活的标签
     *
     * @return string|int|null
     */
    public function getDefaultActiveTab(): string | int | null
    {
        return 'all';
    }

}
