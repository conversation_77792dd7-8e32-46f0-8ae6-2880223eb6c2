<?php

namespace App\Filament\Resources\AnchorResource\RelationManagers;

use App\Filament\Resources\AnchorAccountResource;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class AnchorAccountsRelationManager extends RelationManager
{
    protected static string $relationship = 'anchorAccounts';
    protected static ?string $title = '主播账号';

    //是否只读,如果是true只读,则会隐藏创建,编辑,删除
    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return AnchorAccountResource::form($form);
    }

    public function table(Table $table): Table
    {
        return AnchorAccountResource::table($table);
    }
}
