<?php

namespace App\Filament\Resources\LiveDataResource\RelationManagers;

use App\Filament\Resources\LiveDataCommissionResource;
use App\Models\LiveDataCommission;
use Filament\Forms;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Components\Tab;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class LiveDataCommissionsRelationManager extends RelationManager
{
    protected static string $relationship = 'liveDataCommissions';
    protected static ?string $title = '直播场次佣金';

    //是否只读,如果是true只读,则会隐藏创建,编辑,删除
    public function isReadOnly(): bool
    {
        return false;
    }

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return "直播场次佣金记录";
    }


    public function getTabs(): array
    {
        return [
            'all'               => Tab::make('全部条目'),
            'add_commission'    => Tab::make('添加佣金')
                ->modifyQueryUsing(fn(Builder $query) => $query->whereNot('req_type', 'add_commission')),
            'refund_commission' => Tab::make('佣金退佣')
                ->modifyQueryUsing(fn(Builder $query) => $query->where('req_type', 'refund_commission')),
            // 'payment_commission' => Tab::make('佣金打款')
            //     ->modifyQueryUsing(fn (Builder $query) => $query->where('req_type', 'payment_commission')),
        ];
    }


    public function form(Form $form): Form
    {
        // return LiveDataCommissionResource::form($form);
        return $form
            ->schema([
                Forms\Components\Fieldset::make('场次信息')
                    ->schema([

                        \Filament\Forms\Components\Grid::make(3)
                            ->schema([

                                Forms\Components\Hidden::make('req_username')
                                    ->label('申请人')
                                    ->default(fn() => Auth::user()->name),

                                Forms\Components\Select::make('livedata_id')
                                    ->label('直播场次')
                                    ->relationship('liveData')
                                    ->getOptionLabelFromRecordUsing(fn(Model $record) => "{$record->start_time} - {$record->end_time}  成交金额:{$record->sales} {$record->base_info}")
                                    ->required()
                                    ->disabled()
                                    ->columnSpanFull()
                                    ->default(fn(RelationManager $livewire) => $livewire->ownerRecord->id),

                                Forms\Components\TextInput::make('total_commission')
                                    ->label('累计佣金')
                                    ->required()
                                    ->numeric()
                                    ->disabled()
                                    ->default(fn(RelationManager $livewire) => $livewire->ownerRecord->total_commission),
                                Forms\Components\TextInput::make('returned_commission')
                                    ->label('累计已退佣')
                                    ->required()
                                    ->numeric()
                                    ->disabled()
                                    ->default(fn(RelationManager $livewire) => $livewire->ownerRecord->returned_commission),
                                Forms\Components\TextInput::make('real_commission')
                                    ->label('实际佣金')
                                    ->required()
                                    ->numeric()
                                    ->disabled()
                                    ->default(fn(RelationManager $livewire) => $livewire->ownerRecord->real_commission),
                                // Forms\Components\TextInput::make('payment_commission')
                                //     ->label('累计已结算')
                                //     ->required()
                                //     ->numeric()
                                //     ->disabled()
                                //     ->default(fn (RelationManager $livewire) => $livewire->ownerRecord->payment_commission),
                            ]),
                    ]),

                //添加分割线
                // Forms\Components\Section::make('佣金记录信息')
                //     ->description('这里是本条佣金记录的详细信息, 请认真阅读后才进行操作!')
                //     ->columnSpanFull()
                Forms\Components\Fieldset::make('佣金记录信息')
                    ->schema([
                        Forms\Components\Select::make('req_type')
                            ->label('佣金类型')
                            //model的枚举值getReqTypeList方法
                            ->options(LiveDataCommission::getReqTypeList())
                            ->required(),
                        Forms\Components\TextInput::make('req_amount')
                            ->label('佣金金额')
                            ->required()
                            ->numeric()
                            ->default(0.00),
                        Forms\Components\Textarea::make('req_remark')
                            ->label('备注描述')
                            ->columnSpanFull(),

                        Forms\Components\FileUpload::make('req_images')
                            ->label('备注图片')
                            ->helperText('您可以在这里上传本次佣金的付款到账的截图或者其他相关凭证.')
                            ->multiple()
                            ->image()
                            ->imagePreviewHeight('250')
                            ->columnSpanFull(),
                    ]),


                // Forms\Components\Textarea::make('approval_status')
                //     ->label('当前审批状态')
                //     ->hidden(fn($state)=>!empty($state))
                //     //自定义的类型 转换为 文本字符串
                //     ->formatStateUsing(fn ($state) => LiveDataCommission::getApprovalStatusList()[$state])
                //     ->columnSpanFull(),
            ]);
    }


    public function table(Table $table): Table
    {
        return LiveDataCommissionResource::daya_table($table, 'relation')
            ->columns([
                \EightyNine\Approvals\Tables\Columns\ApprovalStatusColumn::make("approvalStatus.status")
                    ->label('审批状态'),
                Tables\Columns\TextColumn::make('req_username')
                    ->label('申请人')
                    ->searchable(),
                Tables\Columns\TextColumn::make('livedata.base_info')
                    ->label('直播场次'),
                Tables\Columns\TextColumn::make('req_type')
                    ->label('佣金类型')
                    ->formatStateUsing(function ($state) {
                        $reqTypeList = LiveDataCommission::getReqTypeList();
                        if (isset($reqTypeList[$state])) {
                            $reqType = $reqTypeList[$state];
                        } else {
                            $reqType = '不支持的类型';
                        }
                        return $reqType;
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('req_amount')
                    ->label('佣金金额')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('req_remark')
                    ->label('备注信息')
                    //最大长度
                    ->limit(50)
                    ->wrap(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->headerActions([
                // 不允许直接创建直播场次佣金记录
                // Tables\Actions\CreateAction::make()->label("[超管]创建直播场次佣金记录"),
            ]);
    }
}
