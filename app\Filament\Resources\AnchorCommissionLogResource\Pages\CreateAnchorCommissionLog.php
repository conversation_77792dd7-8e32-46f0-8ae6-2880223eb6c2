<?php

namespace App\Filament\Resources\AnchorCommissionLogResource\Pages;

use App\Filament\Resources\AnchorCommissionLogResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateAnchorCommissionLog extends CreateRecord
{
    protected static string $resource = AnchorCommissionLogResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        // 如果是退回佣金，将金额转为负数
        if ($data['change_type'] === 'refund_commission') {
            $data['change_amount'] = -abs($data['change_amount']);
        }

        return static::getModel()::create($data);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
