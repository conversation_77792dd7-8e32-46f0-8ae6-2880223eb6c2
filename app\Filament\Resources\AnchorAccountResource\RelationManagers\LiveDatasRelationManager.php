<?php

namespace App\Filament\Resources\AnchorAccountResource\RelationManagers;

use App\Filament\Resources\LiveDataResource;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;

class LiveDatasRelationManager extends RelationManager
{
    protected static string $relationship = 'liveDatas';
    protected static ?string $title = '直播场次';

    //是否只读,如果是true只读,则会隐藏创建,编辑,删除
    public function isReadOnly(): bool
    {
        return false;
    }

    public function form(Form $form): Form
    {
        return LivedataResource::form($form);
    }

    public function table(Table $table): Table
    {
        return LiveDataResource::table($table);
    }
}
