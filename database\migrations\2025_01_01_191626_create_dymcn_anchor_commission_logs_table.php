<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 主播佣金变动记录表：记录每次佣金的变动情况，用于按月统计
     */
    public function up(): void
    {
        Schema::create('dymcn_anchor_commission_logs', function (Blueprint $table) {
            $table->id();
            $table->integer('anchor_id')->default(0)->comment('所属主播ID');
            $table->integer('anchor_account_id')->default(0)->comment('所属主播账号ID');
            $table->integer('livedata_id')->default(0)->comment('关联直播场次ID');
            $table->integer('commission_id')->default(0)->comment('关联佣金审核记录ID');

            $table->string('change_type')->comment('变动类型：add_commission=添加佣金,refund_commission=退回佣金');
            $table->decimal('change_amount', 10, 2)->default(0.00)->comment('变动金额');

            $table->string('operator')->default('')->comment('操作人');
            $table->text('remark')->nullable()->comment('备注信息');

            $table->timestamps();

            // 添加索引
            $table->index('anchor_id');
            $table->index('anchor_account_id');
            $table->index('livedata_id');
            $table->index('commission_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dymcn_anchor_commission_logs');
    }
};
